import os
import sqlite3
import logging
import json
import re
import glob
from datetime import datetime

logger = logging.getLogger(__name__)

# Create a dedicated logger for execution tracking
execution_logger = logging.getLogger('execution_tracking')
execution_handler = logging.FileHandler('execution_tracking.log')
execution_formatter = logging.Formatter('%(asctime)s - EXECUTION_TRACKING - %(message)s')
execution_handler.setFormatter(execution_formatter)
execution_logger.addHandler(execution_handler)
execution_logger.setLevel(logging.INFO)

def log_execution_tracking_change(test_execution_id, test_suite_id, test_case_id, action_id, old_status, new_status, retry_count, timestamp, operation_type="UPDATE"):
    """
    Log execution tracking changes for debugging and audit purposes

    Args:
        test_execution_id (str): Test execution ID
        test_suite_id (str): Test suite ID
        test_case_id (str): Test case ID
        action_id (str): Action ID
        old_status (str): Previous status
        new_status (str): New status
        retry_count (int): Current retry count
        timestamp (str): Timestamp of change
        operation_type (str): Type of operation (UPDATE, INSERT)
    """
    log_message = f"OPERATION={operation_type} | test_execution_id={test_execution_id} | test_suite_id={test_suite_id} | test_case_id={test_case_id} | action_id={action_id} | old_status={old_status} | new_status={new_status} | retry_count={retry_count} | timestamp={timestamp}"

    # Log to both the main logger and dedicated execution logger
    logger.info(log_message)
    execution_logger.info(log_message)

def sync_execution_data_to_json(test_execution_id, reports_dir):
    """
    Synchronize database execution data to data.json file

    Args:
        test_execution_id (str): Test execution ID to sync
        reports_dir (str): Reports directory path

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Get all execution data for this test execution ID
        cursor.execute('''
        SELECT suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
               status, retry_count, last_error, start_time, end_time, execution_result,
               test_case_id, test_execution_id
        FROM execution_tracking
        WHERE test_execution_id = ?
        ORDER BY test_idx, step_idx
        ''', (test_execution_id,))

        execution_data = cursor.fetchall()
        conn.close()

        if not execution_data:
            logger.warning(f"No execution data found for test_execution_id: {test_execution_id}")
            return False

        # Group data by test case
        test_cases = {}
        suite_id = None

        for row in execution_data:
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id,
             status, retry_count, last_error, start_time, end_time, execution_result,
             test_case_id, exec_id) = row

            if test_case_id not in test_cases:
                test_cases[test_case_id] = {
                    'name': filename.replace('.json', ''),
                    'test_case_id': test_case_id,
                    'filename': filename,
                    'status': 'unknown',
                    'steps': []
                }

            if step_idx is not None:
                # This is a step/action
                step_data = {
                    'action_id': action_id,
                    'action_type': action_type,
                    'status': status,
                    'retry_count': retry_count,
                    'description': action_params,
                    'timestamp': end_time,
                    'test_execution_id': exec_id
                }
                test_cases[test_case_id]['steps'].append(step_data)

        # Determine final status for each test case
        for test_case_id, test_case in test_cases.items():
            steps = test_case['steps']
            if not steps:
                test_case['status'] = 'unknown'
            else:
                # Group steps by action_id to get final status for each action
                action_statuses = {}
                for step in steps:
                    action_id = step['action_id']
                    if action_id not in action_statuses:
                        action_statuses[action_id] = []
                    action_statuses[action_id].append(step)

                # Get final status for each action (last retry)
                final_action_statuses = []
                for action_id, action_steps in action_statuses.items():
                    # Sort by retry_count to get the latest attempt
                    action_steps.sort(key=lambda x: x['retry_count'])
                    final_status = action_steps[-1]['status']
                    final_action_statuses.append(final_status)

                # Test case passes only if all actions pass
                if all(status == 'passed' for status in final_action_statuses):
                    test_case['status'] = 'passed'
                else:
                    test_case['status'] = 'failed'

        # Create the data.json structure
        json_data = {
            'name': f'Test Suite {suite_id}',
            'test_execution_id': test_execution_id,
            'test_suite_id': suite_id,
            'timestamp': datetime.now().isoformat(),
            'status': 'passed' if all(tc['status'] == 'passed' for tc in test_cases.values()) else 'failed',
            'testCases': list(test_cases.values())
        }

        # Find the execution directory
        execution_dirs = [d for d in os.listdir(reports_dir) if test_execution_id in d]
        if execution_dirs:
            execution_dir = os.path.join(reports_dir, execution_dirs[0])
            data_json_path = os.path.join(execution_dir, 'data.json')

            # Write the synchronized data
            with open(data_json_path, 'w') as f:
                json.dump(json_data, f, indent=2)

            logger.info(f"Successfully synchronized execution data to {data_json_path}")
            return True
        else:
            logger.warning(f"No execution directory found for test_execution_id: {test_execution_id}")
            return False

    except Exception as e:
        logger.error(f"Error synchronizing execution data to JSON: {str(e)}")
        return False

# Database file path - make it instance-specific for multi-instance support
def get_db_path():
    """Get the database path, with instance-specific suffix if running multiple instances"""
    base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data')

    # Check if we're running a specific instance
    instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')
    if instance_suffix:
        db_filename = f'test_execution{instance_suffix}.db'
    else:
        db_filename = 'test_execution.db'

    return os.path.join(base_path, db_filename)

# Use the function to get the path
DB_PATH = get_db_path()

def ensure_db_directory():
    """Ensure the directory for the database exists"""
    db_path = get_db_path()
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"Created directory for database: {db_dir}")

def init_db():
    """Initialize the database with required tables"""
    ensure_db_directory()

    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create test_suites table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_suites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        name TEXT,
        status TEXT,
        passed INTEGER,
        failed INTEGER,
        skipped INTEGER,
        timestamp TEXT,
        report_dir TEXT,
        error TEXT
    )
    ''')

    # Create test_cases table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_cases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        name TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        error TEXT
    )
    ''')

    # Create test_steps table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_steps (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        name TEXT,
        action_type TEXT,
        action_id TEXT,
        status TEXT,
        duration TEXT,
        timestamp TEXT,
        screenshot_path TEXT,
        error TEXT,
        enabled INTEGER DEFAULT 1
    )
    ''')

    # Create screenshots table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS screenshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        path TEXT,
        timestamp TEXT,
        action_id TEXT,
        custom_screenshot_name TEXT,
        custom_screenshot_filename TEXT,
        custom_screenshot_path TEXT
    )
    ''')

    # Create environment_variables table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS environment_variables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create execution_tracking table for detailed execution status
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS execution_tracking (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        action_type TEXT,
        action_params TEXT,
        action_id TEXT,
        status TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 0,
        last_error TEXT,
        start_time TEXT,
        end_time TEXT,
        in_progress BOOLEAN DEFAULT 0,
        execution_result TEXT
    )
    ''')

    # Add execution_result column if it doesn't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE execution_tracking ADD COLUMN execution_result TEXT')
    except sqlite3.OperationalError:
        # Column already exists
        pass



    conn.commit()
    conn.close()

    # Update schemas for new features
    update_test_steps_schema()
    update_screenshots_schema()
    update_execution_tracking_schema()

    logger.info("Database initialized successfully")



def update_screenshots_schema():
    """
    Update the screenshots table schema to include custom screenshot fields

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING SCREENSHOTS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if custom screenshot columns exist
        cursor.execute("PRAGMA table_info(screenshots)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add custom screenshot columns if they don't exist
        if 'custom_screenshot_name' not in columns:
            logger.info("Adding custom_screenshot_name column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_name TEXT")

        if 'custom_screenshot_filename' not in columns:
            logger.info("Adding custom_screenshot_filename column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_filename TEXT")

        if 'custom_screenshot_path' not in columns:
            logger.info("Adding custom_screenshot_path column to screenshots table")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN custom_screenshot_path TEXT")

        conn.commit()
        conn.close()

        logger.info("Screenshots table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating screenshots table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_test_steps_schema():
    """
    Update the test_steps table schema to include enabled column for step enable/disable functionality

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING TEST_STEPS TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if enabled column exists
        cursor.execute("PRAGMA table_info(test_steps)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add enabled column if it doesn't exist (default to 1 for enabled)
        if 'enabled' not in columns:
            logger.info("Adding enabled column to test_steps table")
            cursor.execute("ALTER TABLE test_steps ADD COLUMN enabled INTEGER DEFAULT 1")

        conn.commit()
        conn.close()

        logger.info("Test_steps table schema updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating test_steps table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_execution_tracking_schema():
    """
    Update the execution_tracking table schema to include step_idx, action_type, action_params, and action_id columns

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING EXECUTION_TRACKING TABLE SCHEMA ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone() is None:
            logger.info("execution_tracking table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS execution_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                filename TEXT,
                action_type TEXT,
                action_params TEXT,
                action_id TEXT,
                status TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                last_error TEXT,
                start_time TEXT,
                end_time TEXT,
                in_progress BOOLEAN DEFAULT 0,
                execution_result TEXT
            )
            ''')

            # Add execution_result column if it doesn't exist (for existing databases)
            try:
                cursor.execute('ALTER TABLE execution_tracking ADD COLUMN execution_result TEXT')
            except sqlite3.OperationalError:
                # Column already exists
                pass

            logger.info("execution_tracking table created successfully")
        else:
            # Check if step_idx column exists
            try:
                cursor.execute("SELECT step_idx FROM execution_tracking LIMIT 1")
                logger.info("step_idx column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding step_idx column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN step_idx INTEGER")
                logger.info("step_idx column added successfully")

            # Check if action_type column exists
            try:
                cursor.execute("SELECT action_type FROM execution_tracking LIMIT 1")
                logger.info("action_type column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_type column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_type TEXT")
                logger.info("action_type column added successfully")

            # Check if action_params column exists
            try:
                cursor.execute("SELECT action_params FROM execution_tracking LIMIT 1")
                logger.info("action_params column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_params column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_params TEXT")
                logger.info("action_params column added successfully")

            # Check if action_id column exists
            try:
                cursor.execute("SELECT action_id FROM execution_tracking LIMIT 1")
                logger.info("action_id column already exists in execution_tracking table")
            except sqlite3.OperationalError:
                logger.info("Adding action_id column to execution_tracking table...")
                cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_id TEXT")
                logger.info("action_id column added successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully updated execution_tracking table schema")
        return True
    except Exception as e:
        logger.error(f"Error updating execution_tracking table schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def clear_test_tables():
    """
    Clear the test_suites and test_cases tables instead of dropping them

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING TEST TABLES (test_suites, test_cases) ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if tables exist before clearing
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_suites'")
        test_suites_exists = cursor.fetchone() is not None

        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
        test_cases_exists = cursor.fetchone() is not None

        # Clear tables if they exist
        if test_suites_exists:
            logger.info("Clearing test_suites table...")
            cursor.execute("DELETE FROM test_suites")
            suites_cleared = cursor.rowcount
            logger.info(f"test_suites table cleared successfully, removed {suites_cleared} rows")
        else:
            logger.info("test_suites table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')
            logger.info("test_suites table created successfully")

        if test_cases_exists:
            logger.info("Clearing test_cases table...")
            cursor.execute("DELETE FROM test_cases")
            cases_cleared = cursor.rowcount
            logger.info(f"test_cases table cleared successfully, removed {cases_cleared} rows")
        else:
            logger.info("test_cases table does not exist, creating it...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')
            logger.info("test_cases table created successfully")

        # Commit and close
        conn.commit()
        conn.close()

        logger.info("Successfully cleared test tables")
        return True
    except Exception as e:
        logger.error(f"Error clearing test tables: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def track_test_execution(suite_id, test_idx, filename, status, retry_count=0, max_retries=0, error=None, in_progress=False, step_idx=None, action_type=None, action_params=None, action_id=None, execution_result=None, test_case_id=None, test_execution_id=None):
    """
    Track test execution status in the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        filename (str): Test case filename
        status (str): Test execution status (started, running, passed, failed, retrying)
        retry_count (int): Current retry count
        max_retries (int): Maximum number of retries
        error (str): Error message if any
        in_progress (bool): Whether the test is currently in progress
        step_idx (int, optional): Step index within the test case
        action_type (str, optional): Type of action being executed
        action_params (dict, optional): Parameters for the action

    Returns:
        bool: Success status
    """
    try:
        logger.info(f"========== TRACKING TEST EXECUTION ==========")
        logger.info(f"DEBUG: RECEIVED test_idx: {test_idx} (type: {type(test_idx)})")
        logger.info(f"DEBUG: suite_id: {suite_id}, test_idx: {test_idx}, step_idx: {step_idx}")
        logger.info(f"DEBUG: filename: {filename}, action_type: {action_type}")
        logger.info(f"DEBUG: status: {status}, retry: {retry_count}/{max_retries}, in_progress: {in_progress}")

        # Get stack trace to see where this function is being called from
        import traceback
        stack_trace = traceback.format_stack()
        logger.info(f"DEBUG: Called from: {stack_trace[-2]}")

        # Log error if present
        if error:
            logger.info(f"DEBUG: error: {error[:100]}..." if len(str(error)) > 100 else f"DEBUG: error: {error}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        # Convert test_idx to int if it's not already
        try:
            # Make sure test_idx is an integer and not None
            if test_idx is None:
                logger.warning("DEBUG: test_idx is None, using 0 instead")
                test_idx = 0
            else:
                # Force conversion to int to ensure we're using the correct type
                test_idx = int(test_idx)
                logger.info(f"DEBUG: Converted test_idx to int: {test_idx}")
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        # Convert step_idx to int if it's not None and not already an int
        if step_idx is not None:
            try:
                step_idx = int(step_idx)
            except (ValueError, TypeError):
                logger.warning(f"DEBUG: Invalid step_idx value: {step_idx}, using None instead")
                step_idx = None

        # Convert action_params to JSON string if it's a dict
        action_params_str = None
        if action_params:
            if isinstance(action_params, dict):
                action_params_str = json.dumps(action_params)
            else:
                action_params_str = str(action_params)
            logger.info(f"DEBUG: action_params: {action_params_str[:100]}..." if len(str(action_params_str)) > 100 else f"DEBUG: action_params: {action_params_str}")

        # Convert execution_result to JSON string if it's a dict
        execution_result_str = None
        if execution_result:
            if isinstance(execution_result, dict):
                execution_result_str = json.dumps(execution_result)
            else:
                execution_result_str = str(execution_result)
            logger.info(f"DEBUG: execution_result: {execution_result_str[:100]}..." if len(str(execution_result_str)) > 100 else f"DEBUG: execution_result: {execution_result_str}")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if there's an existing entry - prioritize unique ID matching for retries
        existing = None
        existing_id = None

        # For retry scenarios, check by test_execution_id, test_case_id, and action_id first
        if test_execution_id and test_case_id and action_id:
            query = '''SELECT id, retry_count, status FROM execution_tracking
                      WHERE test_execution_id = ? AND test_case_id = ? AND action_id = ?'''
            logger.info(f"DEBUG: Checking for retry entry with unique IDs: test_execution_id={test_execution_id}, test_case_id={test_case_id}, action_id={action_id}")
            cursor.execute(query, (test_execution_id, test_case_id, action_id))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry for retry - ID: {existing_id}, current retry: {current_retry_count}, current status: {current_status}")
                # Increment retry count for retries
                retry_count = current_retry_count + 1
                logger.info(f"DEBUG: Incremented retry count to: {retry_count}")

        # If no unique ID match found, fall back to traditional matching
        if not existing:
            if step_idx is not None:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?'
                logger.info(f"DEBUG: Checking for existing step entry with query: {query} and params: ({suite_id}, {test_idx}, {step_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, step_idx, filename))
            else:
                query = 'SELECT id, retry_count, status FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? AND step_idx IS NULL'
                logger.info(f"DEBUG: Checking for existing test case entry with query: {query} and params: ({suite_id}, {test_idx}, {filename})")
                cursor.execute(query, (suite_id, test_idx, filename))
            existing = cursor.fetchone()
            if existing:
                existing_id, current_retry_count, current_status = existing
                logger.info(f"DEBUG: Found existing entry via traditional matching - ID: {existing_id}")

        logger.info(f"DEBUG: Existing entry found: {existing is not None}")

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if existing:
            # Log the status change for comprehensive tracking
            old_status = current_status if 'current_status' in locals() else 'unknown'
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status=old_status,
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="UPDATE"
            )

            # Update existing entry
            cursor.execute('''
            UPDATE execution_tracking
            SET status = ?, retry_count = ?, max_retries = ?, last_error = ?,
                end_time = ?, in_progress = ?, action_type = ?, action_params = ?, action_id = ?, execution_result = ?,
                test_case_id = ?, test_execution_id = ?
            WHERE id = ?
            ''', (
                status, retry_count, max_retries, error or '',
                timestamp, 1 if in_progress else 0, action_type, action_params_str, action_id, execution_result_str,
                test_case_id, test_execution_id, existing_id
            ))
            logger.info(f"Updated execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")
        else:
            # Log the new entry creation
            log_execution_tracking_change(
                test_execution_id=test_execution_id,
                test_suite_id=suite_id,
                test_case_id=test_case_id,
                action_id=action_id,
                old_status="none",
                new_status=status,
                retry_count=retry_count,
                timestamp=timestamp,
                operation_type="INSERT"
            )

            # Insert new entry
            cursor.execute('''
            INSERT INTO execution_tracking
            (suite_id, test_idx, step_idx, filename, action_type, action_params, action_id, status, retry_count, max_retries, last_error, start_time, end_time, in_progress, execution_result, test_case_id, test_execution_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id, test_idx, step_idx, filename, action_type, action_params_str, action_id, status, retry_count, max_retries, error or '',
                timestamp, timestamp, 1 if in_progress else 0, execution_result_str, test_case_id, test_execution_id
            ))
            logger.info(f"Created execution tracking for test {filename} (idx: {test_idx}, step: {step_idx}): status={status}, retry={retry_count}/{max_retries}")

        # Save step information to test_steps table if step_idx is provided
        if step_idx is not None and action_type is not None:
            try:
                # Extract step name from action_params if available
                step_name = f"Step {step_idx}: {action_type}"
                if action_params and isinstance(action_params, dict):
                    if 'description' in action_params:
                        step_name = action_params['description']
                    elif 'name' in action_params:
                        step_name = action_params['name']

                # Use provided action_id or extract from action_params if available
                if action_id is None:
                    if action_params and isinstance(action_params, dict) and 'action_id' in action_params:
                        action_id = action_params['action_id']
                        logger.info(f"Extracted action_id from action_params: {action_id}")

                # Check if step already exists
                cursor.execute(
                    'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
                    (suite_id, test_idx, step_idx)
                )
                existing_step = cursor.fetchone()

                if existing_step:
                    # Update existing step
                    cursor.execute('''
                    UPDATE test_steps
                    SET name = ?, action_type = ?, status = ?, timestamp = ?, error = ?, action_id = ?
                    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                    ''', (
                        step_name, action_type, status, timestamp, error or '', action_id,
                        suite_id, test_idx, step_idx
                    ))
                    logger.info(f"Updated step information in test_steps table: {step_name}")
                else:
                    # Insert new step
                    cursor.execute('''
                    INSERT INTO test_steps
                    (suite_id, test_idx, step_idx, name, action_type, status, timestamp, error, action_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        suite_id, test_idx, step_idx, step_name, action_type, status, timestamp, error or '', action_id
                    ))
                    logger.info(f"Inserted step information into test_steps table: {step_name}")
            except Exception as step_error:
                logger.error(f"Error saving step information to test_steps table: {str(step_error)}")

        conn.commit()
        conn.close()

        # Trigger data synchronization if we have test_execution_id
        if test_execution_id:
            try:
                from utils.directory_utils import get_reports_directory
                reports_dir = get_reports_directory()
                sync_execution_data_to_json(test_execution_id, reports_dir)
            except Exception as sync_error:
                logger.warning(f"Failed to sync execution data to JSON: {str(sync_error)}")

        return True
    except Exception as e:
        logger.error(f"Error tracking test execution: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def clear_execution_tracking():
    """
    Clear all records from the execution_tracking table

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING EXECUTION TRACKING TABLE ===")

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Get count before clearing
        cursor.execute('SELECT COUNT(*) FROM execution_tracking')
        count_before = cursor.fetchone()[0]
        logger.info(f"Found {count_before} records in execution_tracking table before clearing")

        # Delete all records
        cursor.execute('DELETE FROM execution_tracking')

        # Get count after clearing
        cursor.execute('SELECT COUNT(*) FROM execution_tracking')
        count_after = cursor.fetchone()[0]

        # Commit and close
        conn.commit()
        conn.close()

        logger.info(f"Successfully cleared execution_tracking table. Removed {count_before} records.")
        return True
    except Exception as e:
        logger.error(f"Error clearing execution_tracking table: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_screenshots_for_suite(suite_id):
    """
    Get all screenshots for a specific test suite

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of screenshot dictionaries with filename, path, test_idx, step_idx, and action_id
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all screenshots for this suite
        cursor.execute(
            'SELECT * FROM screenshots WHERE suite_id = ?',
            (suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        screenshots = []
        for row in rows:
            screenshot = {
                'filename': row['filename'],
                'path': row['path'],
                'test_idx': row['test_idx'],
                'step_idx': row['step_idx'],
                'action_id': row['action_id'],
                'timestamp': row['timestamp']
            }
            screenshots.append(screenshot)

        logger.info(f"Found {len(screenshots)} screenshots for suite {suite_id}")
        conn.close()
        return screenshots
    except Exception as e:
        logger.error(f"Error getting screenshots for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_test_steps_for_suite(suite_id):
    """
    Get all test steps for a specific test suite from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        list: List of step dictionaries with all step information
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # First, get all execution tracking entries for this suite
        # This will give us all the steps that were executed
        cursor.execute(
            '''
            SELECT * FROM execution_tracking
            WHERE suite_id = ? AND in_progress = 0
            ORDER BY test_idx, step_idx
            ''',
            (suite_id,)
        )
        execution_rows = cursor.fetchall()

        # Convert execution rows to dictionaries
        steps = []
        for row in execution_rows:
            # Convert row to dict
            step = dict(row)

            # Parse action_params if it's a JSON string
            if step['action_params'] and isinstance(step['action_params'], str):
                try:
                    action_params = json.loads(step['action_params'])
                    # Add relevant fields from action_params
                    if isinstance(action_params, dict):
                        # Add description or message as step name if available
                        if 'description' in action_params:
                            step['name'] = action_params['description']
                        elif 'message' in action_params:
                            step['name'] = action_params['message']
                        else:
                            step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
                except:
                    # If JSON parsing fails, use a default name
                    step['name'] = f"Step {step['step_idx']}: {step['action_type']}"
            else:
                # Default name if no action_params
                step['name'] = f"Step {step['step_idx']}: {step['action_type']}"

            # Now get the screenshot for this step if available
            cursor.execute(
                '''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''',
                (suite_id, step['test_idx'], step['step_idx'])
            )
            screenshot_row = cursor.fetchone()

            if screenshot_row:
                screenshot = dict(screenshot_row)
                # Add screenshot information
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                # Use action_id from screenshot if available
                if screenshot['action_id']:
                    step['action_id'] = screenshot['action_id']

            # If we have an action_id in the step, use it for screenshot paths
            if step['action_id']:
                # Standardize screenshot paths based on action_id
                step['screenshot_filename'] = f"{step['action_id']}.png"
                step['report_screenshot'] = f"{step['action_id']}.png"
                step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

            # Add duration field for report compatibility
            step['duration'] = '0ms'  # We don't track duration currently

            steps.append(step)

        logger.info(f"Found {len(steps)} steps for suite {suite_id}")

        # If we didn't find any steps in execution_tracking, try the test_steps table as fallback
        if not steps:
            logger.info(f"No steps found in execution_tracking, trying test_steps table")
            cursor.execute(
                '''
                SELECT ts.*, s.filename as screenshot_filename, s.path as screenshot_path, s.action_id as screenshot_action_id
                FROM test_steps ts
                LEFT JOIN screenshots s ON ts.suite_id = s.suite_id AND ts.test_idx = s.test_idx AND ts.step_idx = s.step_idx
                WHERE ts.suite_id = ?
                ORDER BY ts.test_idx, ts.step_idx
                ''',
                (suite_id,)
            )
            rows = cursor.fetchall()

            # Convert rows to dictionaries
            for row in rows:
                step = dict(row)

                # Add screenshot information if available
                if step.get('screenshot_filename'):
                    step['screenshot'] = step['screenshot_path']
                    step['report_screenshot'] = step['screenshot_filename']
                    step['resolved_screenshot'] = f"screenshots/{step['screenshot_filename']}"

                # Use action_id from step or screenshot
                if step.get('action_id'):
                    # Use step's action_id
                    pass
                elif step.get('screenshot_action_id'):
                    # Use screenshot's action_id
                    step['action_id'] = step['screenshot_action_id']

                # If we have an action_id, standardize screenshot paths
                if step.get('action_id'):
                    step['screenshot_filename'] = f"{step['action_id']}.png"
                    step['report_screenshot'] = f"{step['action_id']}.png"
                    step['resolved_screenshot'] = f"screenshots/{step['action_id']}.png"

                steps.append(step)

            logger.info(f"Found {len(steps)} steps in test_steps table for suite {suite_id}")

        conn.close()
        return steps
    except Exception as e:
        logger.error(f"Error getting steps for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def resolve_execution_id_to_suite_id(execution_id):
    """
    Resolve a timestamp-based execution ID to the actual test suite UUID.

    Args:
        execution_id (str): Execution ID (could be timestamp-based like 'testsuite_execution_20250627_181306'
                           or actual suite UUID like '90853884-1b79-4f05-8542-f590d5d307a1')

    Returns:
        str: The actual test suite UUID, or the original execution_id if no mapping found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # First, check if this is already a valid suite_id in the database
        cursor.execute('SELECT DISTINCT suite_id FROM execution_tracking WHERE suite_id = ? LIMIT 1', (execution_id,))
        if cursor.fetchone():
            conn.close()
            logger.info(f"Execution ID {execution_id} is already a valid suite_id")
            return execution_id

        # Try to find the actual suite_id using test_execution_id pattern matching
        # The test_execution_id often contains the timestamp that matches the execution folder name
        if 'testsuite_execution_' in execution_id:
            # Extract timestamp from execution_id (e.g., '20250627_181306' from 'testsuite_execution_20250627_181306')
            timestamp_part = execution_id.replace('testsuite_execution_', '')

            # Look for execution tracking entries where test_execution_id contains this timestamp
            cursor.execute('''
                SELECT DISTINCT suite_id, test_execution_id
                FROM execution_tracking
                WHERE test_execution_id LIKE ? OR test_execution_id LIKE ?
                ORDER BY id DESC
                LIMIT 1
            ''', (f'%{timestamp_part}%', f'%{execution_id}%'))

            result = cursor.fetchone()
            if result:
                actual_suite_id = result[0]
                logger.info(f"Resolved execution ID {execution_id} to suite ID {actual_suite_id}")
                conn.close()
                return actual_suite_id

        # If no mapping found, return the original execution_id
        logger.warning(f"Could not resolve execution ID {execution_id} to a suite ID")
        conn.close()
        return execution_id

    except Exception as e:
        logger.error(f"Error resolving execution ID to suite ID: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return execution_id

def get_execution_tracking_for_suite(suite_id):
    """
    Get all execution tracking entries for a specific test suite from the database.
    Automatically resolves timestamp-based execution IDs to actual suite UUIDs.

    Args:
        suite_id (str): Test suite ID (can be timestamp-based execution ID or actual UUID)

    Returns:
        list: List of execution tracking dictionaries with all execution information
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(suite_id)
        logger.info(f"Querying execution tracking for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite with custom screenshot information
        # Order by end_time DESC, then by id DESC to get the most recent execution status for each step
        # The id DESC ensures that if end_time is the same, we get the latest inserted record
        cursor.execute(
            '''
            SELECT et.*, s.custom_screenshot_name, s.custom_screenshot_filename, s.custom_screenshot_path
            FROM execution_tracking et
            LEFT JOIN screenshots s ON et.suite_id = s.suite_id
                AND et.test_idx = s.test_idx
                AND et.step_idx = s.step_idx
            WHERE et.suite_id = ?
            ORDER BY et.test_execution_id DESC, et.test_idx, et.step_idx, et.end_time DESC, et.id DESC
            ''',
            (actual_suite_id,)
        )
        rows = cursor.fetchall()

        # Convert rows to dictionaries
        execution_entries = []
        for row in rows:
            entry = dict(row)

            # Convert action_params from JSON string to dict if it exists
            if entry.get('action_params'):
                try:
                    entry['action_params'] = json.loads(entry['action_params'])
                except:
                    # If JSON parsing fails, keep it as a string
                    pass

            # Log the action_id for debugging
            if entry.get('action_id'):
                logger.info(f"Found action_id {entry['action_id']} for step {entry.get('step_idx')} in test {entry.get('test_idx')}")
            else:
                logger.warning(f"No action_id found for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # If action_params contains action_id, use it if entry doesn't already have one
            if not entry.get('action_id') and entry.get('action_params') and isinstance(entry['action_params'], dict) and entry['action_params'].get('action_id'):
                entry['action_id'] = entry['action_params']['action_id']
                logger.info(f"Using action_id {entry['action_id']} from action_params for step {entry.get('step_idx')} in test {entry.get('test_idx')}")

            # Add timestamp for sorting
            entry['timestamp'] = entry.get('start_time') or entry.get('end_time') or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            execution_entries.append(entry)

        logger.info(f"Found {len(execution_entries)} execution tracking entries for suite {suite_id}")
        conn.close()
        return execution_entries
    except Exception as e:
        logger.error(f"Error getting execution tracking for suite: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_action_id_for_step(suite_id, test_idx, step_idx):
    """
    Get the action_id for a specific step in a test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index

    Returns:
        str: Action ID if found, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # First try to find the action_id in the test_steps table
        cursor.execute(
            'SELECT action_id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in test_steps table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in test_steps, try the screenshots table
        cursor.execute(
            'SELECT action_id FROM screenshots WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in screenshots table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If not found in screenshots, try the execution_tracking table
        cursor.execute(
            'SELECT action_id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        row = cursor.fetchone()

        if row and row[0]:
            action_id = row[0]
            logger.info(f"Found action_id {action_id} in execution_tracking table for step {step_idx} in test {test_idx}")
            conn.close()
            return action_id

        # If we still don't have an action_id, return None
        logger.warning(f"No action_id found for step {step_idx} in test {test_idx}")
        conn.close()
        return None
    except Exception as e:
        logger.error(f"Error getting action_id for step: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_status(suite_id, test_idx=None, filename=None, test_case_id=None):
    """
    Get test execution status from the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int, optional): Test case index
        filename (str, optional): Test case filename

    Returns:
        dict or list: Test execution status(es)
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if test_idx is not None and filename is not None:
            # Get specific test execution - prioritize test_case_id if available
            if test_case_id:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND test_case_id = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, test_case_id)
                )
            else:
                cursor.execute(
                    'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND filename = ? ORDER BY retry_count DESC LIMIT 1',
                    (suite_id, test_idx, filename)
                )
            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None
        else:
            # Get all test executions for the suite
            cursor.execute(
                'SELECT * FROM execution_tracking WHERE suite_id = ? ORDER BY test_idx',
                (suite_id,)
            )
            rows = cursor.fetchall()
            conn.close()

            return [dict(row) for row in rows]
    except Exception as e:
        logger.error(f"Error getting test execution status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None

def get_test_execution_data(execution_id):
    """
    Get test execution data for a specific execution ID

    Args:
        execution_id (str): Execution ID (can be timestamp-based or actual suite UUID)

    Returns:
        list: List of execution data dictionaries containing filename, test_idx, test_case_id, etc.
    """
    try:
        # Resolve the execution ID to actual suite ID
        actual_suite_id = resolve_execution_id_to_suite_id(execution_id)
        logger.info(f"Getting test execution data for resolved suite_id: {actual_suite_id}")

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()

        # Get all execution tracking entries for this suite
        cursor.execute('''
            SELECT DISTINCT filename, test_idx, test_case_id, suite_id
            FROM execution_tracking
            WHERE suite_id = ?
            ORDER BY test_idx
        ''', (actual_suite_id,))

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            logger.warning(f"No execution data found for execution_id: {execution_id} (resolved to: {actual_suite_id})")
            return []

        # Convert to list of dictionaries
        execution_data = []
        for row in rows:
            execution_data.append({
                'filename': row['filename'],
                'test_idx': row['test_idx'],
                'test_case_id': row['test_case_id'],
                'suite_id': row['suite_id']
            })

        logger.info(f"Found {len(execution_data)} test cases for execution_id: {execution_id}")
        return execution_data

    except Exception as e:
        logger.error(f"Error getting test execution data: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def get_final_test_case_status(suite_id, test_case_id=None, filename=None, test_idx=None):
    """
    Get the final status for a test case considering all retries

    Args:
        suite_id (str): Test suite ID
        test_case_id (str, optional): Test case ID
        filename (str, optional): Test case filename
        test_idx (int, optional): Test case index

    Returns:
        dict: Final status information including overall test case status
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Build query based on available identifiers
        # PRIORITIZE UUID-based lookups for reliability
        where_conditions = ['suite_id = ?']
        params = [suite_id]

        if test_case_id:
            # PREFERRED: Use UUID-based lookup
            where_conditions.append('test_case_id = ?')
            params.append(test_case_id)
            logger.debug(f"Using UUID-based lookup: test_case_id={test_case_id}")
        elif filename:
            # FALLBACK: Use filename-based lookup (less reliable)
            where_conditions.append('filename = ?')
            params.append(filename)
            logger.debug(f"Using filename-based lookup: filename={filename}")
        elif test_idx is not None:
            # FALLBACK: Use test_idx-based lookup (legacy)
            where_conditions.append('test_idx = ?')
            params.append(test_idx)
            logger.debug(f"Using test_idx-based lookup: test_idx={test_idx}")
        else:
            logger.warning("No test case identifier provided")
            return {'status': 'unknown', 'actions': []}

        where_clause = ' AND '.join(where_conditions)

        # Get all actions for this test case
        cursor.execute(f'''
            SELECT action_id, status, retry_count, end_time, action_type
            FROM execution_tracking
            WHERE {where_clause}
            ORDER BY action_id, retry_count DESC
        ''', params)

        rows = cursor.fetchall()
        conn.close()

        if not rows:
            return {'status': 'unknown', 'actions': []}

        # Group by action_id and get the latest status for each action
        action_statuses = {}
        for row in rows:
            action_id = row['action_id']
            if action_id not in action_statuses:
                # First entry for this action_id (highest retry_count due to ORDER BY)
                action_statuses[action_id] = {
                    'status': row['status'],
                    'retry_count': row['retry_count'],
                    'end_time': row['end_time'],
                    'action_type': row['action_type']
                }

        # Determine overall test case status
        final_statuses = [action['status'] for action in action_statuses.values()]

        if any(status == 'failed' for status in final_statuses):
            overall_status = 'failed'
        elif any(status == 'passed' for status in final_statuses):
            overall_status = 'passed'
        elif any(status == 'running' for status in final_statuses):
            overall_status = 'running'
        else:
            overall_status = 'unknown'

        return {
            'status': overall_status,
            'actions': action_statuses,
            'total_actions': len(action_statuses)
        }

    except Exception as e:
        logger.error(f"Error getting final test case status: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {'status': 'unknown', 'actions': []}

def reset_test_case_execution_tracking(suite_id, test_idx):
    """
    Reset execution tracking for a specific test case
    This ensures that failures from previous test cases don't affect the current one

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Log detailed information for debugging
        logger.info(f"DEBUG: Resetting execution tracking for test case {test_idx} in suite {suite_id}")

        # Check if suite_id and test_idx are valid
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        if test_idx is None:
            logger.warning("DEBUG: test_idx is None, using 0 instead")
            test_idx = 0

        # Convert test_idx to int if it's not already
        try:
            test_idx = int(test_idx)
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        # Connect to the database
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # First, check what entries exist for this test case
        cursor.execute(
            'SELECT * FROM execution_tracking WHERE suite_id = ? AND test_idx = ?',
            (suite_id, test_idx)
        )
        existing_entries = cursor.fetchall()
        logger.info(f"DEBUG: Found {len(existing_entries)} existing entries for test case {test_idx} in suite {suite_id}")

        # Log the first few entries for debugging
        for i, entry in enumerate(existing_entries[:5]):  # Only log the first 5 entries to avoid flooding the logs
            logger.info(f"DEBUG: Entry {i+1}: {entry}")

        if len(existing_entries) > 5:
            logger.info(f"DEBUG: ... and {len(existing_entries) - 5} more entries")

        # Delete all execution tracking entries for this test case
        delete_query = 'DELETE FROM execution_tracking WHERE suite_id = ? AND test_idx = ?'
        logger.info(f"DEBUG: Executing query: {delete_query} with params: ({suite_id}, {test_idx})")
        cursor.execute(delete_query, (suite_id, test_idx))

        # Log how many rows were deleted
        rows_deleted = cursor.rowcount
        logger.info(f"DEBUG: Deleted {rows_deleted} rows from execution_tracking for test case {test_idx} in suite {suite_id}")

        # Commit the changes
        conn.commit()

        # Verify that the entries were deleted
        cursor.execute(
            'SELECT COUNT(*) FROM execution_tracking WHERE suite_id = ? AND test_idx = ?',
            (suite_id, test_idx)
        )
        remaining_count = cursor.fetchone()[0]
        logger.info(f"DEBUG: After deletion, found {remaining_count} remaining entries for test case {test_idx} in suite {suite_id}")

        # Close the connection
        conn.close()

        logger.info(f"Reset execution tracking for test case {test_idx} in suite {suite_id}")
        return True
    except Exception as e:
        logger.error(f"Error resetting execution tracking for test case {test_idx} in suite {suite_id}: {str(e)}")
        logger.error(traceback.format_exc())  # Log the full traceback for better debugging
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def has_test_case_failures(suite_id, test_idx, force_return_true=False):
    """
    Check if there are any failures in the current test case.
    This is the SINGLE SOURCE OF TRUTH for determining if a test case has failures.
    All code should use this function instead of tracking failures in memory.

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        force_return_true (bool): If True, always return True regardless of actual failures.

    Returns:
        tuple: (has_failures, failure_details)
            - has_failures (bool): True if there are failures, False otherwise
            - failure_details (str): Error message if there are failures, None otherwise
    """
    try:
        # If force_return_true is set, return True immediately without checking the database
        if force_return_true:
            logger.info(f"DEBUG: force_return_true is set, returning True without checking database")
            return True, "Force return true flag is set"

        # Log detailed information for debugging
        logger.info(f"DEBUG: Checking for failures in test case {test_idx} for suite {suite_id}")

        # Validate parameters
        if suite_id is None:
            logger.warning("DEBUG: suite_id is None, using empty string instead")
            suite_id = ""

        if test_idx is None:
            logger.warning("DEBUG: test_idx is None, using 0 instead")
            test_idx = 0

        # Convert test_idx to int if it's not already
        try:
            test_idx = int(test_idx)
        except (ValueError, TypeError):
            logger.warning(f"DEBUG: Invalid test_idx value: {test_idx}, using 0 instead")
            test_idx = 0

        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the current test run start time
        # First, check if there's a running entry for this test case
        query_start_time = '''
        SELECT MIN(created_at) as start_time
        FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND in_progress = 1
        '''
        cursor.execute(query_start_time, (suite_id, test_idx))
        start_time_row = cursor.fetchone()

        if start_time_row and start_time_row['start_time']:
            # We found a running entry, use its timestamp as the start time
            start_time = start_time_row['start_time']
            logger.info(f"DEBUG: Found running entry with start time: {start_time}")
        else:
            # No running entry found, check for the earliest entry for this test case in this run
            query_earliest = '''
            SELECT MIN(created_at) as start_time
            FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ?
            '''
            cursor.execute(query_earliest, (suite_id, test_idx))
            earliest_row = cursor.fetchone()

            if earliest_row and earliest_row['start_time']:
                start_time = earliest_row['start_time']
                logger.info(f"DEBUG: Using earliest entry as start time: {start_time}")
            else:
                # No entries found at all, use a recent timestamp (last 10 minutes)
                from datetime import datetime, timedelta
                start_time = (datetime.now() - timedelta(minutes=10)).strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"DEBUG: No entries found, using default start time: {start_time}")

        # Check for any failed steps in the current test case, only considering entries after the start time
        query1 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND status = "failed" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query1} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query1, (suite_id, test_idx, start_time))
        failed_rows = cursor.fetchall()

        # Log the raw failed rows for debugging
        logger.info(f"DEBUG: Found {len(failed_rows)} failed rows in current run (after {start_time})")
        for i, row in enumerate(failed_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Failed row {i+1}: {row_dict}")

        if len(failed_rows) > 3:
            logger.info(f"DEBUG: ... and {len(failed_rows) - 3} more failed rows")

        # Also check for steps with errors in the current run
        query2 = '''
        SELECT * FROM execution_tracking
        WHERE suite_id = ? AND test_idx = ? AND last_error IS NOT NULL AND last_error != "" AND created_at >= ?
        '''
        logger.info(f"DEBUG: Executing query: {query2} with params: ({suite_id}, {test_idx}, {start_time})")
        cursor.execute(query2, (suite_id, test_idx, start_time))
        error_rows = cursor.fetchall()

        # Log the raw error rows for debugging
        logger.info(f"DEBUG: Found {len(error_rows)} error rows in current run (after {start_time})")
        for i, row in enumerate(error_rows[:3]):  # Only log the first 3 rows to avoid flooding the logs
            row_dict = dict(row)
            logger.info(f"DEBUG: Error row {i+1}: {row_dict}")

        if len(error_rows) > 3:
            logger.info(f"DEBUG: ... and {len(error_rows) - 3} more error rows")

        conn.close()

        # Combine failed and error rows
        all_failures = failed_rows + error_rows

        if all_failures:
            # Get the error message from the first failure
            error_message = None
            for row in all_failures:
                row_dict = dict(row)
                if row_dict.get('last_error'):
                    error_message = row_dict['last_error']
                    break

            logger.info(f"DEBUG: Found {len(all_failures)} total failures in test case {test_idx} for suite {suite_id} in current run")
            if error_message:
                logger.info(f"DEBUG: Error message: {error_message[:100]}..." if len(str(error_message)) > 100 else f"DEBUG: Error message: {error_message}")

            return True, error_message

        logger.info(f"DEBUG: No failures found in test case {test_idx} for suite {suite_id} in current run")
        return False, None
    except Exception as e:
        logger.error(f"Error checking for test case failures: {str(e)}")
        logger.error(traceback.format_exc())  # Log the full traceback for better debugging
        if 'conn' in locals() and conn:
            conn.close()
        return False, None


def get_hook_execution_count(suite_id, test_idx, step_idx=None, action_type=None):
    """
    Get the number of times a hook action has been executed for a specific test case

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int, optional): Step index
        action_type (str, optional): Action type (e.g., 'hookAction')

    Returns:
        int: Number of times the hook action has been executed
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Build the query based on the provided parameters
        query = 'SELECT COUNT(*) FROM execution_tracking WHERE suite_id = ? AND test_idx = ?'
        params = [suite_id, test_idx]

        # Add step_idx condition if provided
        if step_idx is not None:
            query += ' AND step_idx = ?'
            params.append(step_idx)

        # Add action_type condition if provided
        if action_type is not None:
            query += ' AND action_type = ?'
            params.append(action_type)

        # Execute the query
        cursor.execute(query, params)
        count = cursor.fetchone()[0]
        conn.close()

        logger.info(f"Hook execution count for suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}, action_type={action_type}: {count}")
        return count
    except Exception as e:
        logger.error(f"Error getting hook execution count: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return 0

def clear_database():
    """
    Clear all test data from the database but preserve the screenshots table
    This is a less aggressive alternative to reset_database() that doesn't drop tables

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== CLEARING DATABASE (PRESERVING SCREENSHOTS) ===")

        # Check if database file exists
        if not os.path.exists(get_db_path()):
            logger.warning(f"Database file does not exist: {get_db_path()}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute('BEGIN TRANSACTION')

            # Count before clearing
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_before = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_before = cursor.fetchone()[0]

            logger.info(f"Before clearing - Database contains: {suites_count_before} suites, {cases_count_before} cases, {steps_count_before} steps, {screenshots_count_before} screenshots")

            # Clear all tables EXCEPT screenshots
            cursor.execute('DELETE FROM test_suites')
            cursor.execute('DELETE FROM test_cases')
            cursor.execute('DELETE FROM test_steps')
            # DO NOT clear screenshots: cursor.execute('DELETE FROM screenshots')
            logger.info("Cleared all tables except screenshots")

            # Verify the clear operation succeeded
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count_after = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count_after = cursor.fetchone()[0]

            logger.info(f"After clearing - Database contains: {suites_count_after} suites, {cases_count_after} cases, {steps_count_after} steps, {screenshots_count_after} screenshots")

            # Verify screenshots were preserved
            if screenshots_count_after == screenshots_count_before:
                logger.info(f"Successfully preserved {screenshots_count_after} screenshots")
            else:
                logger.warning(f"Screenshot count changed: {screenshots_count_before} before, {screenshots_count_after} after")

            # Commit the transaction
            conn.commit()
            logger.info("Database clearing transaction committed")

            # Verify success
            clear_successful = (suites_count_after == 0 and cases_count_after == 0 and steps_count_after == 0)
            logger.info(f"Database clear successful: {clear_successful}")

            return clear_successful

        except Exception as e:
            # Rollback on error
            conn.rollback()
            logger.error(f"Error during database clearing: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after clear operation")

    except Exception as e:
        logger.error(f"Error in clear_database: {str(e)}")
        logger.error(traceback.format_exc())

        # Try the reset_database function as a last resort
        logger.info("Attempting reset_database as fallback after clear_database failure")
        reset_success = reset_database()
        return reset_success

def save_test_suite(suite_data):
    """
    Save test suite data to the database

    Args:
        suite_data (dict): Test suite data

    Returns:
        str: Suite ID
    """
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    suite_id = suite_data.get('id')
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Insert test suite
    cursor.execute('''
    INSERT INTO test_suites (suite_id, name, status, passed, failed, skipped, timestamp, report_dir, error)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        suite_id,
        suite_data.get('name', 'Unknown Suite'),
        suite_data.get('status', 'unknown'),
        suite_data.get('passed', 0),
        suite_data.get('failed', 0),
        suite_data.get('skipped', 0),
        timestamp,
        suite_data.get('report_dir', ''),
        suite_data.get('error', '')
    ))

    # Insert test cases and steps
    for test_idx, test_case in enumerate(suite_data.get('testCases', [])):
        cursor.execute('''
        INSERT INTO test_cases (suite_id, test_idx, name, status, duration, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            suite_id,
            test_idx,
            test_case.get('name', f'Test {test_idx}'),
            test_case.get('status', 'unknown'),
            test_case.get('duration', '0ms'),
            timestamp
        ))

        for step_idx, step in enumerate(test_case.get('steps', [])):
            # Extract action type from step data
            action_type = None
            if 'action' in step and isinstance(step['action'], dict) and 'type' in step['action']:
                action_type = step['action']['type']
            elif 'type' in step:
                action_type = step['type']
            elif 'name' in step and ':' in step['name']:
                # Try to extract from name (e.g., "Tap: element" -> "Tap")
                action_type = step['name'].split(':', 1)[0].strip()

            cursor.execute('''
            INSERT INTO test_steps (suite_id, test_idx, step_idx, name, action_type, status, duration, timestamp, screenshot_path, error)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id,
                test_idx,
                step_idx,
                step.get('name', f'Step {step_idx}'),
                action_type,
                step.get('status', 'unknown'),
                step.get('duration', '0ms'),
                step.get('timestamp', timestamp),
                step.get('screenshot', ''),
                step.get('error', '')
            ))

            # Save screenshot info if available
            if 'screenshot_filename' in step and step['screenshot_filename']:
                cursor.execute('''
                INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    suite_id,
                    test_idx,
                    step_idx,
                    step.get('screenshot_filename', ''),
                    step.get('screenshot', ''),
                    timestamp
                ))

    conn.commit()
    conn.close()

    logger.info(f"Saved test suite {suite_id} to database")
    return suite_id

def get_test_suite(suite_id):
    """
    Get test suite data from the database

    Args:
        suite_id (str): Test suite ID

    Returns:
        dict: Test suite data
    """
    conn = sqlite3.connect(get_db_path())
    conn.row_factory = sqlite3.Row  # This enables column access by name
    cursor = conn.cursor()

    # Get test suite
    cursor.execute('SELECT * FROM test_suites WHERE suite_id = ?', (suite_id,))
    suite_row = cursor.fetchone()

    if not suite_row:
        conn.close()
        return None

    # Convert to dict
    suite_data = dict(suite_row)

    # Initialize screenshots map
    screenshots_map = {}

    # Get test cases
    cursor.execute('SELECT * FROM test_cases WHERE suite_id = ? ORDER BY test_idx', (suite_id,))
    test_cases = []

    for test_case_row in cursor.fetchall():
        test_case = dict(test_case_row)
        test_idx = test_case['test_idx']

        # Get steps for this test case
        cursor.execute('''
        SELECT * FROM test_steps
        WHERE suite_id = ? AND test_idx = ?
        ORDER BY step_idx
        ''', (suite_id, test_idx))

        steps = []
        for step_row in cursor.fetchall():
            step = dict(step_row)
            step_idx = step['step_idx']

            # Get screenshot for this step - prioritize standardized screenshots
            cursor.execute('''
            SELECT * FROM screenshots
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ?
            ''', (suite_id, test_idx, step_idx, f"step_{test_idx}_{step_idx}.png"))

            standardized_screenshot = cursor.fetchone()

            if standardized_screenshot:
                # Use the standardized screenshot
                screenshot = dict(standardized_screenshot)
                step['screenshot'] = screenshot['path']
                step['screenshot_filename'] = screenshot['filename']
                step['report_screenshot'] = screenshot['filename']
                step['resolved_screenshot'] = f"screenshots/{screenshot['filename']}"

                logger.info(f"Found standardized screenshot for step {step_idx}: {screenshot['filename']}")
            else:
                # Fall back to any screenshot for this step
                cursor.execute('''
                SELECT * FROM screenshots
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (suite_id, test_idx, step_idx))

                screenshot_row = cursor.fetchone()
                if screenshot_row:
                    screenshot = dict(screenshot_row)
                    step['screenshot'] = screenshot['path']
                    step['screenshot_filename'] = screenshot['filename']

                    # Also add the standardized format for the report
                    standardized_name = f"step_{test_idx}_{step_idx}.png"
                    step['report_screenshot'] = standardized_name
                    step['resolved_screenshot'] = f"screenshots/{standardized_name}"

                    logger.info(f"Using non-standardized screenshot for step {step_idx}: {screenshot['filename']}")

            # If we found any screenshot, add it to the screenshots map
            if 'screenshot_filename' in step:
                # Add to screenshots map for easy access in the report
                test_case_name = test_case.get('name', f'test_{test_idx}')
                test_case_name = re.sub(r'[^a-zA-Z0-9]', '', test_case_name)
                screenshot_key = f"{test_case_name}_{step_idx}"

                screenshots_map[screenshot_key] = {
                    'filename': step['screenshot_filename'],
                    'path': step['screenshot'],
                    'action_type': step.get('name', '').split(':')[0].strip() if ':' in step.get('name', '') else step.get('name', '')
                }

            steps.append(step)

        test_case['steps'] = steps
        test_cases.append(test_case)

    suite_data['testCases'] = test_cases
    suite_data['screenshots_map'] = screenshots_map

    conn.close()
    return suite_data

def get_latest_test_suite():
    """
    Get the latest test suite from the database

    Returns:
        dict: Latest test suite data
    """
    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    cursor.execute('SELECT suite_id FROM test_suites ORDER BY id DESC LIMIT 1')
    result = cursor.fetchone()

    conn.close()

    if result:
        return get_test_suite(result[0])
    return None

def save_screenshot_info(suite_id, test_idx, step_idx, filename, path, action_id=None):
    """
    Save screenshot information to the database and ensure a standardized version exists

    Args:
        suite_id (str, optional): Test suite ID. If None or empty, will be treated as an empty string.
        test_idx (int): Test case index
        step_idx (int): Step index
        filename (str): Screenshot filename
        path (str): Screenshot path
        action_id (str, optional): Action ID from the test case

    Returns:
        bool: Success status
    """
    # Ensure suite_id is a string, even if None is passed
    if suite_id is None:
        suite_id = ''

    logger.info(f"=== SAVING SCREENSHOT TO DATABASE ===")
    logger.info(f"suite_id: {suite_id}")
    logger.info(f"test_idx: {test_idx}")
    logger.info(f"step_idx: {step_idx}")
    logger.info(f"filename: {filename}")
    logger.info(f"path: {path}")
    logger.info(f"action_id: {action_id}")

    # Ensure test_idx and step_idx are integers
    try:
        test_idx = int(test_idx)
        step_idx = int(step_idx)
    except (ValueError, TypeError):
        logger.warning(f"Invalid test_idx or step_idx values: test_idx={test_idx}, step_idx={step_idx}")
        # Default to 0 for test_idx and 1 for step_idx if conversion fails
        if not isinstance(test_idx, int):
            test_idx = 0
        if not isinstance(step_idx, int):
            # Start from 1 instead of 0 to match UI display
            step_idx = 1

    logger.info(f"Using test_idx={test_idx}, step_idx={step_idx} after validation")

    conn = sqlite3.connect(get_db_path())
    cursor = conn.cursor()

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    logger.info(f"timestamp: {timestamp}")

    # If action_id is not provided, try to get it from the execution_tracking table
    if not action_id:
        try:
            cursor.execute('''
            SELECT action_id FROM execution_tracking
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ''', (suite_id, test_idx, step_idx))
            row = cursor.fetchone()
            if row and row[0]:
                action_id = row[0]
                logger.info(f"Found action_id {action_id} in execution_tracking table for step {step_idx} in test {test_idx}")
        except Exception as e:
            logger.error(f"Error getting action_id from execution_tracking: {str(e)}")

    # If we still don't have an action_id, try to get it from the test_steps table
    if not action_id:
        try:
            cursor.execute('''
            SELECT action_id FROM test_steps
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ''', (suite_id, test_idx, step_idx))
            row = cursor.fetchone()
            if row and row[0]:
                action_id = row[0]
                logger.info(f"Found action_id {action_id} in test_steps table for step {step_idx} in test {test_idx}")
        except Exception as e:
            logger.error(f"Error getting action_id from test_steps: {str(e)}")

    # Create a standardized filename without timestamp
    standardized_filename = f"step_{test_idx}_{step_idx}.png"

    # If we have an action_id, use it for the filename
    if action_id:
        standardized_filename = f"{action_id}.png"
        logger.info(f"Using action_id {action_id} for screenshot filename")

    # Create a standardized path
    standardized_path = f"screenshots/{standardized_filename}"

    # Also create a standardized version of the screenshot file
    try:
        # Get the app's static screenshots directory
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        app_screenshots_dir = os.path.join(app_dir, 'app', 'static', 'screenshots')

        # Source path (with timestamp)
        source_path = None

        # Try different ways to find the source path
        if os.path.isabs(path) and os.path.exists(path):
            # Absolute path
            source_path = path
        elif path.startswith('screenshots/'):
            # Path relative to app's static directory
            potential_path = os.path.join(app_screenshots_dir, path.replace('screenshots/', ''))
            if os.path.exists(potential_path):
                source_path = potential_path
        elif os.path.exists(os.path.join(app_screenshots_dir, filename)):
            # Just the filename in app's static directory
            source_path = os.path.join(app_screenshots_dir, filename)
        elif os.path.exists(os.path.join(app_screenshots_dir, path)):
            # Path relative to app's static directory without 'screenshots/' prefix
            source_path = os.path.join(app_screenshots_dir, path)

        # If we still don't have a valid source path, try to find any file with test_idx and step_idx in the name
        if not source_path or not os.path.exists(source_path):
            pattern = f"*{test_idx}*{step_idx}*.png"
            matching_files = glob.glob(os.path.join(app_screenshots_dir, pattern))
            if matching_files:
                # Use the most recent file
                matching_files.sort(key=os.path.getmtime, reverse=True)
                source_path = matching_files[0]
                logger.info(f"Found matching screenshot: {source_path}")

        # If we still don't have a valid source path, use latest.png as fallback
        if not source_path or not os.path.exists(source_path):
            latest_path = os.path.join(app_screenshots_dir, 'latest.png')
            if os.path.exists(latest_path):
                source_path = latest_path
                logger.info(f"Using latest.png as fallback for step {test_idx}_{step_idx}")

        # Target path (standardized)
        target_path = os.path.join(app_screenshots_dir, standardized_filename)

        # Copy the screenshot to the standardized filename
        if source_path and os.path.exists(source_path):
            import shutil
            # Only copy if source and target are different files
            if source_path != target_path:
                logger.info(f"Copying from {source_path} to {target_path}")
                shutil.copy2(source_path, target_path)
                logger.info(f"Created standardized screenshot: {source_path} -> {target_path}")
            else:
                logger.info(f"Source and target are the same file, skipping copy: {source_path}")
        else:
            logger.warning(f"Could not find source screenshot for step {test_idx}_{step_idx}")
    except Exception as e:
        logger.error(f"Error creating standardized screenshot: {str(e)}")
        import traceback
        traceback.print_exc()

    # Check if record already exists for this suite_id, test_idx, and step_idx
    cursor.execute('''
    SELECT id FROM screenshots
    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
    ''', (suite_id, test_idx, step_idx))

    existing = cursor.fetchone()

    if existing:
        # Update existing record
        logger.info(f"Updating existing screenshot record with ID: {existing[0]}")
        cursor.execute('''
        UPDATE screenshots
        SET filename = ?, path = ?, timestamp = ?, action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (standardized_filename, standardized_path, timestamp, action_id, suite_id, test_idx, step_idx))
    else:
        # Insert new record with standardized filename
        logger.info(f"Inserting new screenshot record for suite_id={suite_id}, test_idx={test_idx}, step_idx={step_idx}")
        cursor.execute('''
        INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp, action_id)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (suite_id, test_idx, step_idx, standardized_filename, standardized_path, timestamp, action_id))

    # Also update the test_steps table with the standardized path if there's a matching step
    cursor.execute('''
    UPDATE test_steps
    SET screenshot_path = ?, action_id = ?
    WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
    ''', (standardized_path, action_id, suite_id, test_idx, step_idx))

    steps_updated = cursor.rowcount
    logger.info(f"Updated {steps_updated} test step records with screenshot path and action_id")

    # Commit changes and close connection
    try:
        conn.commit()
        logger.info(f"Successfully committed changes to database")
    except Exception as commit_error:
        logger.error(f"Error committing changes to database: {str(commit_error)}")
        conn.rollback()
        conn.close()
        return False

    conn.close()
    logger.info(f"Closed database connection")

    logger.info(f"=== SUCCESSFULLY SAVED SCREENSHOT INFO ===")
    logger.info(f"suite_id: {suite_id}, test_idx: {test_idx}, step_idx: {step_idx}")
    logger.info(f"filename: {standardized_filename}, path: {standardized_path}, action_id: {action_id}")
    return True

def update_test_suite(suite_id, test_suite_data):
    """
    Update test suite data in the database

    Args:
        suite_id (str): Test suite ID
        test_suite_data (dict): Test suite data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test suite
        cursor.execute('''
        UPDATE test_suites
        SET name = ?, status = ?, timestamp = ?
        WHERE suite_id = ?
        ''', (
            test_suite_data.get('name', ''),
            test_suite_data.get('status', 'updated'),
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            suite_id
        ))

        # If report_dir is provided, update it
        if 'report_dir' in test_suite_data:
            cursor.execute('''
            UPDATE test_suites
            SET report_dir = ?
            WHERE suite_id = ?
            ''', (
                test_suite_data.get('report_dir', ''),
                suite_id
            ))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error updating test suite in database: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_test_suites():
    """
    Get all test suites from the database

    Returns:
        list: List of test suite summary data
    """
    conn = sqlite3.connect(get_db_path())
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, suite_id, name, status, passed, failed, skipped, timestamp, report_dir
    FROM test_suites
    ORDER BY id DESC
    ''')

    suites = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return suites

def delete_report_by_name(report_name):
    """
    Delete a report from the database by name

    Args:
        report_name (str): Name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE name = ?", (report_name,))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with name: {report_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by name: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def delete_report_by_dir(dir_name):
    """
    Delete a report from the database by directory name

    Args:
        dir_name (str): Directory name of the report to delete

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Find the suite_id for the report
        cursor.execute("SELECT suite_id FROM test_suites WHERE report_dir LIKE ?", (f"%{dir_name}%",))
        result = cursor.fetchone()

        if not result:
            logger.warning(f"No report found with directory: {dir_name}")
            conn.close()
            return False

        suite_id = result[0]
        logger.info(f"Found report with suite_id: {suite_id}")

        # Delete all related data
        cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
        screenshots_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
        steps_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
        cases_deleted = cursor.rowcount

        cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
        suites_deleted = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Deleted report data: {screenshots_deleted} screenshots, {steps_deleted} steps, {cases_deleted} cases, {suites_deleted} suites")
        return True
    except Exception as e:
        logger.error(f"Error deleting report by directory: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def get_all_reports():
    """
    Get all reports from the database

    Returns:
        list: List of report data
    """
    try:
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Query to get all test suites with report data
        cursor.execute('''
        SELECT
            ts.suite_id,
            ts.name AS suite_name,
            ts.status,
            ts.passed,
            ts.failed,
            ts.skipped,
            ts.timestamp,
            ts.report_dir,
            COUNT(tc.id) AS test_case_count
        FROM
            test_suites ts
        LEFT JOIN
            test_cases tc ON ts.suite_id = tc.suite_id
        WHERE
            ts.report_dir IS NOT NULL AND ts.report_dir != ''
        GROUP BY
            ts.suite_id
        ORDER BY
            ts.timestamp DESC
        ''')

        reports = []
        for row in cursor.fetchall():
            row_dict = dict(row)

            # Skip reports with invalid or missing report directories
            if not row_dict.get('report_dir'):
                continue

            # Get the report directory name
            report_dir = row_dict['report_dir']
            dir_name = os.path.basename(report_dir)

            # Create the report URL
            report_url = f"/reports/{dir_name}/mainreport.html"

            # Check if there's a ZIP file
            from app.utils.directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            zip_path = os.path.join(reports_dir, f"{dir_name}.zip")
            zip_url = f"/api/reports/download_zip/{dir_name}.zip" if os.path.exists(zip_path) else None

            # Also check in the report directory itself
            if not os.path.exists(zip_path):
                report_parent_dir = os.path.dirname(report_dir)
                alt_zip_path = os.path.join(report_parent_dir, f"{dir_name}.zip")
                if os.path.exists(alt_zip_path):
                    zip_path = alt_zip_path
                    zip_url = f"/api/reports/download_zip/{dir_name}.zip"

            # Build the report data structure
            reports.append({
                'filename': dir_name,
                'suite_name': row_dict['suite_name'],
                'status': row_dict['status'] or 'Unknown',
                'passed': row_dict['passed'] or 0,
                'failed': row_dict['failed'] or 0,
                'skipped': row_dict['skipped'] or 0,
                'creation_time': row_dict['timestamp'],
                'report_id': dir_name,
                'url': report_url,
                'zip_url': zip_url,
                'test_case_count': row_dict['test_case_count'] or 0,
                'allure_report_url': None  # Currently not supported, could be added in the future
            })

        conn.close()
        return reports

    except Exception as e:
        logger.error(f"Error getting reports: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []

def reset_database():
    """
    Reset the database by dropping and recreating all tables
    Preserves the execution_tracking table for retry tracking
    Preserves the screenshots table to maintain history between retries

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== RESETTING DATABASE ===")

        # Check if database file exists
        if not os.path.exists(get_db_path()):
            logger.warning(f"Database file does not exist: {get_db_path()}")
            # Create the database file and initialize it
            init_db()
            logger.info("Database initialized - it was empty to begin with")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Begin transaction
        conn.execute('BEGIN TRANSACTION')

        try:
            # Save execution tracking data before dropping tables
            cursor.execute('SELECT * FROM execution_tracking')
            execution_tracking_data = cursor.fetchall()
            logger.info(f"Saved {len(execution_tracking_data)} execution tracking records before reset")

            # Save screenshots data before reset - this is critical for retry functionality
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            logger.info(f"Preserving {screenshots_count} screenshots during database reset")

            # Drop all tables except execution_tracking and screenshots
            cursor.execute('DROP TABLE IF EXISTS test_suites')
            cursor.execute('DROP TABLE IF EXISTS test_cases')
            cursor.execute('DROP TABLE IF EXISTS test_steps')
            # Explicitly preserving screenshots table - DO NOT DROP
            # cursor.execute('DROP TABLE IF EXISTS screenshots')
            logger.info("Dropped all tables except screenshots and execution_tracking")

            # Recreate all tables
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                name TEXT,
                status TEXT,
                passed INTEGER,
                failed INTEGER,
                skipped INTEGER,
                timestamp TEXT,
                report_dir TEXT,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                name TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 0,
                error TEXT
            )
            ''')

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suite_id TEXT,
                test_idx INTEGER,
                step_idx INTEGER,
                name TEXT,
                action_type TEXT,
                action_id TEXT,
                status TEXT,
                duration TEXT,
                timestamp TEXT,
                screenshot_path TEXT,
                error TEXT
            )
            ''')

            # Ensure screenshots table exists but don't recreate it (to preserve data)
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='screenshots'")
            if not cursor.fetchone():
                logger.info("Screenshots table doesn't exist, creating it")
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS screenshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    path TEXT,
                    timestamp TEXT,
                    action_id TEXT
                )
                ''')
            else:
                logger.info("Screenshots table exists, preserving it for retry functionality")

            # Check if execution_tracking table exists, create it if not
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
            if not cursor.fetchone():
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_tracking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    suite_id TEXT,
                    test_idx INTEGER,
                    step_idx INTEGER,
                    filename TEXT,
                    action_type TEXT,
                    action_params TEXT,
                    action_id TEXT,
                    status TEXT,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 0,
                    last_error TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    in_progress BOOLEAN DEFAULT 0
                )
                ''')
                logger.info("Created execution_tracking table")

            # Commit transaction
            conn.commit()
            logger.info("Database reset successful - tables reset while preserving screenshots")

            # Verify tables are empty except screenshots
            cursor.execute('SELECT COUNT(*) FROM test_suites')
            suites_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_cases')
            cases_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM test_steps')
            steps_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM screenshots')
            screenshots_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]

            logger.info(f"After reset - Database contains: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
            logger.info(f"Screenshots preserved: {screenshots_count}")

            # Verify reset was successful
            reset_successful = (suites_count == 0 and cases_count == 0 and steps_count == 0)
            logger.info(f"Database reset successful: {reset_successful}")
            return reset_successful

        except Exception as e:
            # Rollback transaction if there's an error
            conn.rollback()
            logger.error(f"Error during database reset: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Close connection
            conn.close()
            logger.info("Database connection closed after reset operation")

    except Exception as e:
        logger.error(f"Error in reset_database: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def check_database_state():
    """
    Check the current state of the database and return a summary

    Returns:
        dict: Summary of database state
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Get counts from all tables
        cursor.execute('SELECT COUNT(*) FROM test_suites')
        suites_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_cases')
        cases_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM test_steps')
        steps_count = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM screenshots')
        screenshots_count = cursor.fetchone()[0]

        # Check if execution_tracking table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='execution_tracking'")
        if cursor.fetchone():
            cursor.execute('SELECT COUNT(*) FROM execution_tracking')
            tracking_count = cursor.fetchone()[0]
        else:
            tracking_count = 0

        # Get sample data if available
        sample_suites = []
        if suites_count > 0:
            cursor.execute('SELECT suite_id, name, status, timestamp FROM test_suites LIMIT 3')
            sample_suites = cursor.fetchall()

        conn.close()

        state = {
            'suites_count': suites_count,
            'cases_count': cases_count,
            'steps_count': steps_count,
            'screenshots_count': screenshots_count,
            'tracking_count': tracking_count,
            'sample_suites': sample_suites
        }

        logger.info(f"Database state: {suites_count} suites, {cases_count} cases, {steps_count} steps, {screenshots_count} screenshots, {tracking_count} tracking entries")
        if sample_suites:
            logger.info(f"Sample suites: {sample_suites}")

        return state
    except Exception as e:
        logger.error(f"Error checking database state: {str(e)}")
        return {
            'error': str(e),
            'suites_count': -1,
            'cases_count': -1,
            'steps_count': -1,
            'screenshots_count': -1,
            'tracking_count': -1,
            'sample_suites': []
        }



# Initialize the database when the module is imported
init_db()

def get_test_case_by_id(test_case_id):
    """
    Get a test case by its ID

    Args:
        test_case_id (str): Test case ID

    Returns:
        dict: Test case data including actions
    """
    try:
        # Check if the test_case_id contains a timestamp (format: Name_YYYYMMDD_HHMMSS.json)
        # If it does, extract the base name
        base_name = test_case_id
        if '_20' in test_case_id and test_case_id.endswith('.json'):
            # Extract the base name (everything before the timestamp)
            parts = test_case_id.split('_')
            timestamp_index = -1
            for i, part in enumerate(parts):
                if part.startswith('20') and len(part) >= 8:  # Looks like a timestamp starting with 20xx
                    timestamp_index = i
                    break

            if timestamp_index > 0:
                base_name = '_'.join(parts[:timestamp_index])
                logger.info(f"Extracted base name '{base_name}' from test case ID: {test_case_id}")

        # First, check if we have a test case file with the exact ID
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        test_cases_dir = os.path.join(app_dir, 'test_cases')
        test_case_file = os.path.join(test_cases_dir, test_case_id)

        # If the test case ID doesn't end with .json, add it
        if not test_case_file.endswith('.json'):
            test_case_file += '.json'

        logger.info(f"Looking for test case file: {test_case_file}")

        if os.path.exists(test_case_file):
            # Load the test case from the file
            with open(test_case_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case {test_case_id} from file")
                return test_case

        # If the exact file doesn't exist, try to find a file with the base name
        if base_name != test_case_id:
            base_file = os.path.join(test_cases_dir, f"{base_name}.json")
            logger.info(f"Looking for test case with base name: {base_file}")

            if os.path.exists(base_file):
                # Load the test case from the file
                with open(base_file, 'r') as f:
                    test_case = json.load(f)
                    logger.info(f"Loaded test case {base_name} from file (using base name)")
                    return test_case

        # If still not found, try to find any file that starts with the base name
        matching_files = [f for f in os.listdir(test_cases_dir) if f.startswith(f"{base_name}_") and f.endswith('.json')]
        if matching_files:
            # Sort by modification time (newest first)
            matching_files.sort(key=lambda f: os.path.getmtime(os.path.join(test_cases_dir, f)), reverse=True)
            latest_file = os.path.join(test_cases_dir, matching_files[0])
            logger.info(f"Found matching test case file: {latest_file}")

            # Load the test case from the file
            with open(latest_file, 'r') as f:
                test_case = json.load(f)
                logger.info(f"Loaded test case from matching file: {matching_files[0]}")
                return test_case

        # If no file exists, try to load from the database
        conn = sqlite3.connect(get_db_path())
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Get the test case from the database
        cursor.execute('''
        SELECT tc.*, ts.name as suite_name
        FROM test_cases tc
        JOIN test_suites ts ON tc.suite_id = ts.suite_id
        WHERE tc.id = ?
        ''', (test_case_id,))

        test_case_row = cursor.fetchone()

        if not test_case_row:
            # Try with the base name
            if base_name != test_case_id:
                cursor.execute('''
                SELECT tc.*, ts.name as suite_name
                FROM test_cases tc
                JOIN test_suites ts ON tc.suite_id = ts.suite_id
                WHERE tc.name LIKE ?
                ''', (f"{base_name}%",))

                test_case_row = cursor.fetchone()

            if not test_case_row:
                logger.warning(f"Test case not found: {test_case_id}")
                conn.close()
                return None

        # Convert to dict
        test_case = dict(test_case_row)

        # Get the actions for this test case
        # For now, we'll use a placeholder since we don't store actions in the database
        # In a real implementation, you would load the actions from a file or another table
        test_case['actions'] = []

        conn.close()
        logger.info(f"Loaded test case {test_case_id} from database")
        return test_case
    except Exception as e:
        logger.error(f"Error getting test case by ID: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# Check initial database state
logger.info("Checking initial database state...")
check_database_state()

def save_screenshot_info(suite_id, test_idx, step_idx, filename, path, retry_number=None, action_id=None, custom_screenshot_name=None, custom_screenshot_filename=None, custom_screenshot_path=None):
    """
    Save screenshot information to the database

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        filename (str): Screenshot filename
        path (str): Screenshot path
        retry_number (int, optional): Retry number if this is a retry screenshot
        action_id (str, optional): Unique action ID for the action that created this screenshot
        custom_screenshot_name (str, optional): Custom screenshot name for takeScreenshot actions
        custom_screenshot_filename (str, optional): Custom screenshot filename for takeScreenshot actions
        custom_screenshot_path (str, optional): Custom screenshot path for takeScreenshot actions

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Skip if no action_id is provided - this prevents duplicate entries
        if not action_id:
            logger.warning("Not saving screenshot to database because action_id is missing")
            return False

        # Skip if the filename is "latest.png" - we don't want to save this to the database
        if filename == "latest.png":
            logger.info("Not saving latest.png to database")
            return True

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Add retry number to filename if provided
        if retry_number is not None:
            filename = f"retry{retry_number}_{filename}"
            path = path.replace(filename.split('_', 1)[1], filename)

        # Ensure filename matches action_id
        if not filename.startswith(action_id):
            logger.info(f"Standardizing filename to match action_id: {action_id}.png")
            filename = f"{action_id}.png"
            # Update path to match the new filename
            if '/' in path:
                path = path.rsplit('/', 1)[0] + '/' + filename
            else:
                path = filename

        # Log the action_id for debugging
        logger.info(f"Saving screenshot with action_id: {action_id}")

        # Check if a record with this action_id already exists
        cursor.execute('''
        SELECT id FROM screenshots WHERE action_id = ?
        ''', (action_id,))
        existing_by_action_id = cursor.fetchone()

        if existing_by_action_id:
            # Update the existing record with this action_id
            logger.info(f"Updating existing screenshot record with action_id: {action_id}")
            cursor.execute('''
            UPDATE screenshots
            SET filename = ?, path = ?, timestamp = ?, suite_id = ?, test_idx = ?, step_idx = ?,
                custom_screenshot_name = ?, custom_screenshot_filename = ?, custom_screenshot_path = ?
            WHERE action_id = ?
            ''', (
                filename,
                path,
                timestamp,
                suite_id,
                test_idx,
                step_idx,
                custom_screenshot_name,
                custom_screenshot_filename,
                custom_screenshot_path,
                action_id
            ))
            conn.commit()
            conn.close()
            logger.info(f"Updated screenshot info for action_id: {action_id}")
            return True

        # Check if a record with this suite_id, test_idx, and step_idx already exists
        cursor.execute('''
        SELECT id, action_id FROM screenshots
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (suite_id, test_idx, step_idx))

        existing = cursor.fetchone()

        if existing:
            # If there's already a record for this step but with a different action_id,
            # log a warning but still update it
            if existing[1] and existing[1] != action_id:
                logger.warning(f"Replacing existing action_id {existing[1]} with new action_id {action_id} for step {test_idx}_{step_idx}")

            # Always update with action_id
            logger.info(f"Updating existing screenshot record with action_id: {action_id}")
            cursor.execute('''
            UPDATE screenshots
            SET filename = ?, path = ?, timestamp = ?, action_id = ?,
                custom_screenshot_name = ?, custom_screenshot_filename = ?, custom_screenshot_path = ?
            WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
            ''', (
                filename,
                path,
                timestamp,
                action_id,
                custom_screenshot_name,
                custom_screenshot_filename,
                custom_screenshot_path,
                suite_id,
                test_idx,
                step_idx
            ))
        else:
            # Insert new record
            logger.info(f"Inserting new screenshot record with action_id: {action_id}")
            cursor.execute('''
            INSERT INTO screenshots (suite_id, test_idx, step_idx, filename, path, timestamp, action_id,
                                   custom_screenshot_name, custom_screenshot_filename, custom_screenshot_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                suite_id,
                test_idx,
                step_idx,
                filename,
                path,
                timestamp,
                action_id,
                custom_screenshot_name,
                custom_screenshot_filename,
                custom_screenshot_path
            ))

        conn.commit()
        conn.close()

        logger.info(f"Saved screenshot info to database: {filename} with action_id: {action_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving screenshot info to database: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def check_screenshot_exists(action_id):
    """
    Check if a screenshot with the given action_id already exists in the database

    Args:
        action_id (str): The action_id to check

    Returns:
        bool: True if a screenshot with this action_id exists, False otherwise
    """
    if not action_id:
        return False

    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if a record with this action_id exists
        cursor.execute('SELECT id FROM screenshots WHERE action_id = ?', (action_id,))
        result = cursor.fetchone()

        conn.close()
        return result is not None
    except Exception as e:
        logger.error(f"Error checking if screenshot exists: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def update_test_step_action_type(suite_id, test_idx, step_idx, action_type, action_id=None):
    """
    Update the action_type field in the test_steps table

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_type (str): Action type
        action_id (str, optional): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if the step exists
        cursor.execute(
            'SELECT id FROM test_steps WHERE suite_id = ? AND test_idx = ? AND step_idx = ?',
            (suite_id, test_idx, step_idx)
        )
        existing = cursor.fetchone()

        if existing:
            # Update existing step
            if action_id:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?, action_id = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    action_id,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type and action_id for step {test_idx}_{step_idx} to {action_type}, {action_id}")
            else:
                cursor.execute('''
                UPDATE test_steps
                SET action_type = ?
                WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
                ''', (
                    action_type,
                    suite_id,
                    test_idx,
                    step_idx
                ))
                logger.info(f"Updated action_type for step {test_idx}_{step_idx} to {action_type}")
        else:
            # Step doesn't exist, log a warning
            logger.warning(f"Step {test_idx}_{step_idx} not found in test_steps table, cannot update action_type")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_type in test_steps table: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_id(suite_id, test_idx, step_idx, action_id):
    """
    Update the action_id field in the test_steps and screenshots tables

    Args:
        suite_id (str): Test suite ID
        test_idx (int): Test case index
        step_idx (int): Step index
        action_id (str): Unique action ID for the action

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update test_steps table
        cursor.execute('''
        UPDATE test_steps
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        steps_updated = cursor.rowcount
        logger.info(f"Updated action_id for {steps_updated} steps in test_steps table")

        # Update screenshots table
        cursor.execute('''
        UPDATE screenshots
        SET action_id = ?
        WHERE suite_id = ? AND test_idx = ? AND step_idx = ?
        ''', (
            action_id,
            suite_id,
            test_idx,
            step_idx
        ))
        screenshots_updated = cursor.rowcount
        logger.info(f"Updated action_id for {screenshots_updated} screenshots in screenshots table")

        conn.commit()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"Error updating action_id: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

def clear_screenshots():
    """
    Clear screenshots table in the database

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import sqlite3
        import traceback

        logger.info("Clearing screenshots from database...")

        # Connect to the database
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Delete all records from the screenshots table
        cursor.execute("DELETE FROM screenshots")
        screenshots_deleted = cursor.rowcount

        # Also clear any standardized_screenshots table if it exists
        try:
            cursor.execute("DELETE FROM standardized_screenshots")
            std_screenshots_deleted = cursor.rowcount
        except sqlite3.OperationalError:
            std_screenshots_deleted = 0
            logger.info("standardized_screenshots table does not exist")

        # Commit and close
        conn.commit()
        conn.close()

        # Vacuum the database to reclaim space
        conn = sqlite3.connect(get_db_path())
        conn.execute("PRAGMA foreign_keys = OFF")
        conn.execute("VACUUM")
        conn.execute("PRAGMA foreign_keys = ON")
        conn.close()

        logger.info(f"Successfully cleared {screenshots_deleted} screenshots and {std_screenshots_deleted} standardized screenshots")
        return True

    except Exception as e:
        logger.error(f"Error in clear_screenshots: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def save_environment_variable(name, value, description=None):
    """
    Save or update an environment variable in the database

    Args:
        name (str): Variable name
        value (str): Variable value
        description (str, optional): Variable description

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Use INSERT OR REPLACE to handle both new and existing variables
        cursor.execute("""
            INSERT OR REPLACE INTO environment_variables (name, value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        """, (name, value, description))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' saved successfully")
        return True

    except Exception as e:
        logger.error(f"Error saving environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def get_environment_variable(name):
    """
    Get an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        dict: Variable data or None if not found
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            WHERE name = ?
        """, (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            return {
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            }
        return None

    except Exception as e:
        logger.error(f"Error getting environment variable '{name}': {str(e)}")
        return None


def get_all_environment_variables():
    """
    Get all environment variables from the database

    Returns:
        list: List of environment variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT name, value, description, created_at, updated_at
            FROM environment_variables
            ORDER BY name
        """)

        rows = cursor.fetchall()
        conn.close()

        variables = []
        for row in rows:
            variables.append({
                'name': row[0],
                'value': row[1],
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })

        return variables

    except Exception as e:
        logger.error(f"Error getting all environment variables: {str(e)}")
        return []


def delete_environment_variable(name):
    """
    Delete an environment variable from the database

    Args:
        name (str): Variable name

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("DELETE FROM environment_variables WHERE name = ?", (name,))

        conn.commit()
        conn.close()

        logger.info(f"Environment variable '{name}' deleted successfully")
        return True

    except Exception as e:
        logger.error(f"Error deleting environment variable '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


def update_action_enabled_state(action_id, enabled):
    """
    Update the enabled state of an action in the database

    Args:
        action_id (str): Action ID
        enabled (bool): Whether the action is enabled

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Update the enabled state in the actions table
        cursor.execute("""
            UPDATE actions SET enabled = ? WHERE action_id = ?
        """, (enabled, action_id))

        # If no rows were affected, the action might not exist yet
        if cursor.rowcount == 0:
            # Try to insert a new record (this might be needed for legacy actions)
            cursor.execute("""
                INSERT OR IGNORE INTO actions (action_id, enabled) VALUES (?, ?)
            """, (action_id, enabled))

        conn.commit()
        conn.close()

        logger.info(f"Action '{action_id}' enabled state updated to {enabled}")
        return True

    except Exception as e:
        logger.error(f"Error updating action enabled state for '{action_id}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False


# Environment Management Functions
def get_environments():
    """
    Get all environments from the database

    Returns:
        list: List of environment dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, is_active, created_at, updated_at
            FROM env_environments
            ORDER BY name
        """)

        environments = []
        for row in cursor.fetchall():
            environments.append({
                'id': row[0],
                'name': row[1],
                'is_active': bool(row[2]),
                'created_at': row[3],
                'updated_at': row[4]
            })

        conn.close()
        return environments

    except Exception as e:
        logger.error(f"Error getting environments: {str(e)}")
        return []


def get_variables(environment_id):
    """
    Get all variables for a specific environment

    Args:
        environment_id (int): Environment ID

    Returns:
        list: List of variable dictionaries
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, name, initial_value, current_value, type, created_at, updated_at
            FROM environment_variables
            WHERE environment_id = ?
            ORDER BY name
        """, (environment_id,))

        variables = []
        for row in cursor.fetchall():
            variables.append({
                'id': row[0],
                'name': row[1],
                'initial_value': row[2] or '',
                'current_value': row[3] or '',
                'type': row[4] or 'default',
                'created_at': row[5],
                'updated_at': row[6]
            })

        conn.close()
        return variables

    except Exception as e:
        logger.error(f"Error getting variables for environment {environment_id}: {str(e)}")
        return []


def create_environment(name):
    """
    Create a new environment

    Args:
        name (str): Environment name

    Returns:
        int: Environment ID if successful, None otherwise
    """
    try:
        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO env_environments (name, is_active, created_at, updated_at)
            VALUES (?, 0, datetime('now'), datetime('now'))
        """, (name,))

        environment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Environment '{name}' created with ID {environment_id}")
        return environment_id

    except Exception as e:
        logger.error(f"Error creating environment '{name}': {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None


def create_variable(environment_id, name, initial_value, current_value=None, var_type='default'):
    """
    Create a new environment variable

    Args:
        environment_id (int): Environment ID
        name (str): Variable name
        initial_value (str): Initial value
        current_value (str): Current value (defaults to initial_value)
        var_type (str): Variable type

    Returns:
        int: Variable ID if successful, None otherwise
    """
    try:
        if current_value is None:
            current_value = initial_value

        conn = sqlite3.connect(get_db_path())
        cursor = conn.cursor()

        # Check if variable already exists for this environment
        cursor.execute("""
            SELECT id FROM environment_variables
            WHERE environment_id = ? AND name = ?
        """, (environment_id, name))
        existing = cursor.fetchone()

        if existing:
            # Update existing variable
            cursor.execute("""
                UPDATE environment_variables
                SET current_value = ?, updated_at = datetime('now')
                WHERE environment_id = ? AND name = ?
            """, (current_value, environment_id, name))
            variable_id = existing[0]
            logger.info(f"Variable '{name}' updated for environment {environment_id}")
        else:
            # Create new variable
            cursor.execute("""
                INSERT INTO environment_variables
                (environment_id, name, initial_value, current_value, type, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (environment_id, name, initial_value, current_value, var_type))

            variable_id = cursor.lastrowid
            logger.info(f"Variable '{name}' created for environment {environment_id}")

        conn.commit()
        conn.close()

        return variable_id

    except Exception as e:
        logger.error(f"Error creating variable '{name}' for environment {environment_id}: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return None
