#!/usr/bin/env python3
"""
Test the export system with UUID-based lookups
"""
import sys
import os
import json

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.custom_report_generator import CustomReportGenerator

def test_export_system():
    """Test the export system with the actual report ID"""
    print("Testing export system with UUID-based lookups...")
    
    # Use the actual report ID from the export folder
    report_id = "testsuite_execution_20250627_181306"
    app_root_path = os.path.join(os.path.dirname(__file__), 'app')
    
    print(f"Report ID: {report_id}")
    print(f"App root path: {app_root_path}")
    
    try:
        # Initialize the CustomReportGenerator
        generator = CustomReportGenerator(report_id, app_root_path)
        
        print(f"Execution ID extracted: {generator.execution_id}")
        
        # Test the _load_test_data method
        print("\n=== Testing _load_test_data ===")
        test_data = generator._load_test_data()
        
        if test_data:
            print(f"✅ Test data loaded successfully")
            
            # Check both possible keys for test cases
            test_cases = test_data.get('testCases', test_data.get('test_cases', []))
            print(f"Found {len(test_cases)} test cases")
            
            # Find the Delivery & CNC test case
            delivery_test_case = None
            for test_case in test_cases:
                if 'Delivery' in test_case.get('name', '') and 'CNC' in test_case.get('name', ''):
                    delivery_test_case = test_case
                    break
            
            if delivery_test_case:
                print(f"✅ Found Delivery & CNC test case")
                print(f"   Name: {delivery_test_case.get('name', '').split()[0:3]}")  # First 3 words
                print(f"   Test Case ID: {delivery_test_case.get('test_case_id', 'None')}")
                print(f"   Original Status: {delivery_test_case.get('status')}")
                
                # Test the status determination
                print("\n=== Testing status determination ===")
                final_status = generator._determine_final_test_case_status(delivery_test_case)
                print(f"Final status from generator: {final_status}")
                
                if final_status == 'Passed':
                    print("✅ Status correctly determined as 'Passed'")
                    return True
                else:
                    print(f"❌ Status incorrectly determined as '{final_status}'")
                    return False
            else:
                print("❌ Could not find Delivery & CNC test case")
                return False
        else:
            print("❌ Failed to load test data")
            return False
            
    except Exception as e:
        print(f"❌ Error testing export system: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_database_lookup():
    """Test direct database lookup for the test case"""
    print("\n" + "="*60)
    print("TESTING DIRECT DATABASE LOOKUP")
    print("="*60)
    
    try:
        from utils.database import get_final_test_case_status
        
        # Test with the migrated UUID
        suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"
        filename = "Delivery & CNC Stop"  # From our database analysis
        
        print(f"Testing with suite_id: {suite_id}")
        print(f"Testing with filename: {filename}")
        
        result = get_final_test_case_status(
            suite_id=suite_id,
            filename=filename
        )
        
        print(f"Database result: {result}")
        
        if result and result.get('status') == 'passed':
            print("✅ Direct database lookup returns 'passed'")
            return True
        else:
            print(f"❌ Direct database lookup failed or returned wrong status")
            return False
            
    except Exception as e:
        print(f"❌ Error in direct database lookup: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_filename_matching():
    """Test the filename matching logic"""
    print("\n" + "="*60)
    print("TESTING FILENAME MATCHING LOGIC")
    print("="*60)
    
    # Test case name from data.json (with extra formatting)
    test_case_name = """Delivery & CNC
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            28 actions"""
    
    print(f"Original test case name: {repr(test_case_name)}")
    
    # Clean the name like the CustomReportGenerator does
    import re
    clean_test_name = test_case_name.strip()
    clean_test_name = re.sub(r'\n.*', '', clean_test_name)  # Remove everything after first newline
    
    print(f"Cleaned test case name: {repr(clean_test_name)}")
    
    # Try various filename patterns
    filenames_to_try = [
        clean_test_name,
        f"{clean_test_name} Stop",
        f"{clean_test_name} Retry",
        f"{clean_test_name} Remove"
    ]
    
    print(f"Filenames to try: {filenames_to_try}")
    
    # Test each filename against the database
    try:
        from utils.database import get_final_test_case_status
        suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"
        
        for filename in filenames_to_try:
            result = get_final_test_case_status(
                suite_id=suite_id,
                filename=filename
            )
            
            if result and result.get('status') != 'unknown':
                print(f"✅ Found match with filename: '{filename}' -> Status: {result.get('status')}")
                return True
            else:
                print(f"❌ No match with filename: '{filename}'")
        
        print("❌ No filename patterns matched")
        return False
        
    except Exception as e:
        print(f"❌ Error in filename matching test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Export System with UUID-based Lookups")
    print("="*60)
    
    # Test 1: Direct database lookup
    db_success = test_direct_database_lookup()
    
    # Test 2: Filename matching logic
    filename_success = test_filename_matching()
    
    # Test 3: Full export system
    export_success = test_export_system()
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Direct database lookup: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"Filename matching logic: {'✅ PASS' if filename_success else '❌ FAIL'}")
    print(f"Export system test: {'✅ PASS' if export_success else '❌ FAIL'}")
    
    if all([db_success, filename_success, export_success]):
        print("\n🎉 All tests PASSED! The UUID-based system is working correctly.")
        print("The 'Delivery & CNC' test case should now show correct status in export reports.")
    else:
        print("\n⚠️  Some tests failed. Further investigation needed.")
    
    print("\n📋 Next steps:")
    print("1. Restart the Flask server to pick up the new functions")
    print("2. Test the export functionality through the web interface")
    print("3. Verify that new HTML reports show correct status")
