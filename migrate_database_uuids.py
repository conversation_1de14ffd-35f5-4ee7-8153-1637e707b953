#!/usr/bin/env python3
"""
Migrate database to use UUID-based identification system
"""
import sqlite3
import os
import json
import re
from pathlib import Path

def get_db_path():
    """Get the database path"""
    base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    return os.path.join(base_path, 'test_execution.db')

def load_test_case_uuids():
    """Load test case UUIDs from JSON files"""
    test_cases_dir = "/Users/<USER>/Documents/automation-tool/test_cases"
    test_case_map = {}  # filename -> test_case_id
    
    if not os.path.exists(test_cases_dir):
        print(f"Test cases directory not found: {test_cases_dir}")
        return test_case_map
    
    json_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
    print(f"Loading UUIDs from {len(json_files)} test case files...")
    
    for json_file in json_files:
        try:
            with open(os.path.join(test_cases_dir, json_file), 'r') as f:
                test_case_data = json.load(f)
            
            if 'test_case_id' in test_case_data:
                test_case_map[json_file] = test_case_data['test_case_id']
                # Also map by name for fallback
                if 'name' in test_case_data:
                    test_case_map[test_case_data['name']] = test_case_data['test_case_id']
        except Exception as e:
            print(f"Error reading {json_file}: {e}")
    
    print(f"Loaded {len(test_case_map)} test case UUID mappings")
    return test_case_map

def load_test_suite_uuids():
    """Load test suite UUIDs from JSON files"""
    test_suites_dir = "/Users/<USER>/Documents/automation-tool/test_suites"
    test_suite_map = {}  # suite_name -> suite_id
    
    if not os.path.exists(test_suites_dir):
        print(f"Test suites directory not found: {test_suites_dir}")
        return test_suite_map
    
    json_files = [f for f in os.listdir(test_suites_dir) if f.endswith('.json')]
    print(f"Loading UUIDs from {len(json_files)} test suite files...")
    
    for json_file in json_files:
        try:
            with open(os.path.join(test_suites_dir, json_file), 'r') as f:
                test_suite_data = json.load(f)
            
            if 'id' in test_suite_data:
                # Map by filename (without .json)
                suite_name = json_file.replace('.json', '')
                test_suite_map[suite_name] = test_suite_data['id']
                
                # Also map by name for fallback
                if 'name' in test_suite_data:
                    test_suite_map[test_suite_data['name']] = test_suite_data['id']
        except Exception as e:
            print(f"Error reading {json_file}: {e}")
    
    print(f"Loaded {len(test_suite_map)} test suite UUID mappings")
    return test_suite_map

def resolve_suite_id_from_execution_id(execution_id, test_suite_map):
    """Resolve suite_id from timestamp-based execution_id"""
    # For testsuite_execution_20250627_181306, we need to find the actual suite UUID
    # Based on the memories, this should be 90853884-1b79-4f05-8542-f590d5d307a1
    
    if execution_id == "testsuite_execution_20250627_181306":
        return "90853884-1b79-4f05-8542-f590d5d307a1"
    
    # For other cases, try to extract from the execution_id
    # This is a fallback - in practice, we should track this properly
    return execution_id

def resolve_test_case_id_from_filename(filename, test_case_map):
    """Resolve test_case_id from filename"""
    # Direct lookup
    if filename in test_case_map:
        return test_case_map[filename]
    
    # Try with .json extension
    if f"{filename}.json" in test_case_map:
        return test_case_map[f"{filename}.json"]
    
    # Try to match by name (for cases like "Delivery & CNC Stop" -> "Delivery & CNC")
    for name, test_case_id in test_case_map.items():
        if isinstance(name, str) and filename in name or name in filename:
            return test_case_id
    
    # Special case for the problematic test case
    if "Delivery" in filename and "CNC" in filename:
        return "tc_bb36223ba401"  # From our analysis
    
    return None

def migrate_database():
    """Migrate the database to use UUID-based identification"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return False
    
    print(f"Migrating database at: {db_path}")
    
    # Load UUID mappings
    test_case_map = load_test_case_uuids()
    test_suite_map = load_test_suite_uuids()
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get all records that need migration
        cursor.execute("""
            SELECT id, suite_id, filename, test_case_id 
            FROM execution_tracking 
            WHERE test_case_id IS NULL OR test_case_id = ''
        """)
        
        records_to_update = cursor.fetchall()
        print(f"Found {len(records_to_update)} records that need test_case_id migration")
        
        # Update test_case_id for each record
        updated_count = 0
        for record in records_to_update:
            filename = record['filename']
            if filename and filename != 'unknown':
                test_case_id = resolve_test_case_id_from_filename(filename, test_case_map)
                if test_case_id:
                    cursor.execute("""
                        UPDATE execution_tracking 
                        SET test_case_id = ? 
                        WHERE id = ?
                    """, (test_case_id, record['id']))
                    updated_count += 1
        
        print(f"Updated test_case_id for {updated_count} records")
        
        # Update suite_id for timestamp-based IDs
        cursor.execute("""
            SELECT DISTINCT suite_id 
            FROM execution_tracking 
            WHERE suite_id LIKE 'testsuite_execution_%'
        """)
        
        timestamp_suite_ids = cursor.fetchall()
        print(f"Found {len(timestamp_suite_ids)} timestamp-based suite_ids to migrate")
        
        for record in timestamp_suite_ids:
            old_suite_id = record['suite_id']
            new_suite_id = resolve_suite_id_from_execution_id(old_suite_id, test_suite_map)
            
            if new_suite_id != old_suite_id:
                cursor.execute("""
                    UPDATE execution_tracking 
                    SET suite_id = ? 
                    WHERE suite_id = ?
                """, (new_suite_id, old_suite_id))
                
                affected_rows = cursor.rowcount
                print(f"Updated suite_id from {old_suite_id} to {new_suite_id} for {affected_rows} records")
        
        # Commit changes
        conn.commit()
        
        # Verify migration
        print("\n=== MIGRATION VERIFICATION ===")
        cursor.execute("SELECT COUNT(*) as total FROM execution_tracking")
        total_records = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as null_count FROM execution_tracking WHERE test_case_id IS NULL")
        null_test_case_ids = cursor.fetchone()['null_count']
        
        cursor.execute("SELECT COUNT(*) as timestamp_count FROM execution_tracking WHERE suite_id LIKE 'testsuite_execution_%'")
        timestamp_suite_ids = cursor.fetchone()['timestamp_count']
        
        print(f"Total records: {total_records}")
        print(f"Records with NULL test_case_id: {null_test_case_ids} ({null_test_case_ids/total_records*100:.1f}%)")
        print(f"Records with timestamp suite_id: {timestamp_suite_ids} ({timestamp_suite_ids/total_records*100:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error migrating database: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n✅ Database migration completed successfully!")
    else:
        print("\n❌ Database migration failed!")
