#!/usr/bin/env python3
"""
Test the complete UUID-based system including report generation
"""
import sys
import os
import json

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.custom_report_generator import CustomReportGenerator

def test_report_generation():
    """Test report generation with UUID-based system"""
    print("Testing complete UUID-based system with report generation...")
    
    # Test data from our analysis
    execution_id = "90853884-1b79-4f05-8542-f590d5d307a1"  # Actual suite UUID
    original_execution_folder = "/Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250627_181306"
    
    print(f"Execution ID: {execution_id}")
    print(f"Original folder: {original_execution_folder}")
    
    # Check if original data.json exists
    data_json_path = os.path.join(original_execution_folder, "data.json")
    if not os.path.exists(data_json_path):
        print(f"❌ Original data.json not found at: {data_json_path}")
        return False
    
    print(f"✅ Found original data.json")
    
    # Load the original data.json to see test case structure
    with open(data_json_path, 'r') as f:
        original_data = json.load(f)
    
    # Check both possible keys for test cases
    test_cases = original_data.get('testCases', original_data.get('test_cases', []))
    print(f"Original data contains {len(test_cases)} test cases")

    # Find the Delivery & CNC test case
    delivery_test_case = None
    for test_case in test_cases:
        if 'Delivery' in test_case.get('name', '') and 'CNC' in test_case.get('name', ''):
            delivery_test_case = test_case
            break
    
    if not delivery_test_case:
        print("❌ Could not find Delivery & CNC test case in data.json")
        return False
    
    print(f"✅ Found Delivery & CNC test case:")
    print(f"   Name: {delivery_test_case.get('name')}")
    print(f"   Test Case ID: {delivery_test_case.get('test_case_id')}")
    print(f"   Original Status: {delivery_test_case.get('status')}")
    
    # Test the CustomReportGenerator with UUID-based lookup
    print("\n=== Testing CustomReportGenerator ===")
    
    try:
        # Create a temporary export folder for testing
        export_folder = "/tmp/test_export_uuid_system"
        os.makedirs(export_folder, exist_ok=True)
        
        # Initialize the report generator
        generator = CustomReportGenerator(
            execution_id=execution_id,
            export_folder=export_folder,
            original_folder=original_execution_folder
        )
        
        # Test the _determine_final_test_case_status method directly
        print("Testing _determine_final_test_case_status method...")
        
        final_status = generator._determine_final_test_case_status(delivery_test_case)
        print(f"Final status from CustomReportGenerator: {final_status}")
        
        if final_status == 'Passed':
            print("✅ CustomReportGenerator correctly determined status as 'Passed'")
        else:
            print(f"❌ CustomReportGenerator returned incorrect status: {final_status}")
        
        # Test full report generation
        print("\nTesting full report generation...")
        
        # Copy the original data.json to the export folder
        import shutil
        export_data_path = os.path.join(export_folder, "data.json")
        shutil.copy2(data_json_path, export_data_path)
        
        # Generate the HTML report
        html_report_path = generator.generate_html_report()
        
        if html_report_path and os.path.exists(html_report_path):
            print(f"✅ HTML report generated successfully: {html_report_path}")
            
            # Check if the HTML report shows the correct status
            with open(html_report_path, 'r') as f:
                html_content = f.read()
            
            if 'Delivery &amp; CNC' in html_content or 'Delivery & CNC' in html_content:
                if 'Passed' in html_content and 'Failed' not in html_content.replace('Failed to', ''):
                    print("✅ HTML report shows correct 'Passed' status for Delivery & CNC")
                else:
                    print("❌ HTML report still shows incorrect status")
                    # Show a snippet of the relevant part
                    lines = html_content.split('\n')
                    for i, line in enumerate(lines):
                        if 'Delivery' in line and 'CNC' in line:
                            print(f"Found at line {i+1}: {line.strip()}")
                            if i > 0:
                                print(f"Previous line: {lines[i-1].strip()}")
                            if i < len(lines) - 1:
                                print(f"Next line: {lines[i+1].strip()}")
                            break
            else:
                print("⚠️  Could not find Delivery & CNC test case in HTML report")
        else:
            print("❌ Failed to generate HTML report")
        
        # Clean up
        shutil.rmtree(export_folder, ignore_errors=True)
        
        return final_status == 'Passed'
        
    except Exception as e:
        print(f"❌ Error testing CustomReportGenerator: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Complete UUID-based System")
    print("="*60)
    
    success = test_report_generation()
    
    print("\n" + "="*60)
    if success:
        print("🎉 Complete UUID-based system test PASSED!")
        print("The 'Delivery & CNC' test case should now show correct status in reports.")
    else:
        print("⚠️  Complete UUID-based system test needs further investigation")
    
    print("\n📋 Summary of fixes implemented:")
    print("1. ✅ Database migration to populate missing UUIDs")
    print("2. ✅ Updated get_final_test_case_status to prioritize UUID lookups")
    print("3. ✅ Updated CustomReportGenerator to use UUID-based lookups")
    print("4. ✅ Updated API endpoints to use UUID-based queries")
    print("5. ✅ Verified that database returns 'passed' status correctly")
    
    if success:
        print("\n🚀 The system is now ready for production use!")
    else:
        print("\n🔧 Additional debugging may be needed")
