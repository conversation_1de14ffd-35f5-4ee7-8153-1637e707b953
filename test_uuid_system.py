#!/usr/bin/env python3
"""
Test the UUID-based system to ensure it works correctly
"""
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.database import get_final_test_case_status

def test_uuid_based_lookup():
    """Test UUID-based lookup for the Delivery & CNC test case"""
    print("Testing UUID-based lookup system...")
    
    # Test data from our analysis
    suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"  # Migrated UUID
    test_case_id = "tc_bb36223ba401"  # From the JSON file
    
    print(f"Suite ID: {suite_id}")
    print(f"Test Case ID: {test_case_id}")
    
    # Test UUID-based lookup
    print("\n=== Testing UUID-based lookup ===")
    result = get_final_test_case_status(
        suite_id=suite_id,
        test_case_id=test_case_id
    )
    
    print(f"Result: {result}")
    
    if result and result.get('status') == 'passed':
        print("✅ UUID-based lookup PASSED - Status is 'passed'")
    else:
        print("❌ UUID-based lookup FAILED - Status is not 'passed'")
    
    # Test filename-based lookup (fallback)
    print("\n=== Testing filename-based lookup (fallback) ===")
    result_filename = get_final_test_case_status(
        suite_id=suite_id,
        filename="Delivery & CNC Stop"
    )
    
    print(f"Result: {result_filename}")
    
    if result_filename and result_filename.get('status') == 'passed':
        print("✅ Filename-based lookup PASSED - Status is 'passed'")
    else:
        print("❌ Filename-based lookup FAILED - Status is not 'passed'")
    
    # Compare results
    print("\n=== Comparison ===")
    if result and result_filename:
        uuid_status = result.get('status')
        filename_status = result_filename.get('status')
        
        if uuid_status == filename_status:
            print(f"✅ Both methods agree: {uuid_status}")
        else:
            print(f"❌ Methods disagree: UUID={uuid_status}, Filename={filename_status}")
    
    return result

def test_api_endpoint():
    """Test the API endpoint with the new UUID system"""
    import requests
    
    print("\n" + "="*60)
    print("TESTING API ENDPOINT")
    print("="*60)
    
    try:
        # Test the API endpoint
        url = "http://localhost:8080/api/execution/get-suite-status-from-db"
        data = {"execution_id": "testsuite_execution_20250627_181306"}
        
        print(f"Testing API endpoint: {url}")
        print(f"Request data: {data}")
        
        response = requests.post(url, json=data, timeout=10)
        
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.json()}")
        
        if response.status_code == 200:
            response_data = response.json()
            if 'test_cases' in response_data:
                print("✅ API endpoint PASSED - Returned test cases")
                
                # Check if our test case is in the results
                for test_case in response_data['test_cases']:
                    if 'Delivery' in test_case.get('filename', ''):
                        print(f"Found Delivery & CNC test case: {test_case}")
                        if test_case.get('status') == 'passed':
                            print("✅ Test case status is 'passed' in API response")
                        else:
                            print(f"❌ Test case status is '{test_case.get('status')}' in API response")
                        break
            else:
                print("❌ API endpoint returned no test cases")
        else:
            print(f"❌ API endpoint FAILED - Status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️  Could not connect to API endpoint - server may not be running")
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")

if __name__ == "__main__":
    print("🧪 Testing UUID-based System")
    print("="*60)
    
    # Test database functions
    result = test_uuid_based_lookup()
    
    # Test API endpoint
    test_api_endpoint()
    
    print("\n" + "="*60)
    print("Test completed!")
    
    if result and result.get('status') == 'passed':
        print("🎉 Overall result: UUID system is working correctly!")
    else:
        print("⚠️  Overall result: UUID system needs further investigation")
