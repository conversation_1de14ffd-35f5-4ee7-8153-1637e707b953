#!/usr/bin/env python3
"""
Debug the database reconstruction process
"""
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.database import get_execution_tracking_for_suite, resolve_execution_id_to_suite_id

def debug_database_reconstruction():
    """Debug the database reconstruction process"""
    print("Debugging database reconstruction...")
    
    execution_id = "testsuite_execution_20250627_181306"
    print(f"Original execution_id: {execution_id}")
    
    # Test the resolution
    actual_suite_id = resolve_execution_id_to_suite_id(execution_id)
    print(f"Resolved suite_id: {actual_suite_id}")
    
    # Get execution tracking data
    execution_data = get_execution_tracking_for_suite(actual_suite_id)
    print(f"Found {len(execution_data)} execution tracking records")
    
    if execution_data:
        # Group by test case
        test_cases_data = {}
        for record in execution_data:
            filename = record.get('filename', 'unknown')
            test_case_id = record.get('test_case_id')
            
            if filename not in test_cases_data:
                test_cases_data[filename] = {
                    'filename': filename,
                    'test_case_id': test_case_id,
                    'actions': []
                }
            
            test_cases_data[filename]['actions'].append({
                'action_id': record.get('action_id'),
                'status': record.get('status'),
                'action_type': record.get('action_type'),
                'step_idx': record.get('step_idx')
            })
        
        print(f"\nGrouped into {len(test_cases_data)} test cases:")
        for filename, data in test_cases_data.items():
            print(f"  {filename}: {len(data['actions'])} actions, test_case_id: {data['test_case_id']}")
            
            # Check if this is the Delivery & CNC test case
            if 'Delivery' in filename and 'CNC' in filename:
                print(f"    ✅ Found Delivery & CNC test case!")
                print(f"    Actions: {[a['action_id'] for a in data['actions'][:5]]}...")  # First 5 actions
    
    return execution_data

if __name__ == "__main__":
    debug_database_reconstruction()
