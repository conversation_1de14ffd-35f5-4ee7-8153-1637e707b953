#!/usr/bin/env python3
"""
Debug script to examine test execution data for the failing test case
"""
import sqlite3
import os
import sys

def get_db_path():
    """Get the database path"""
    base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    return os.path.join(base_path, 'test_execution.db')

def examine_test_data():
    """Examine the test execution data for the failing test case"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return
    
    print(f"Examining database at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # First, let's see what execution IDs we have
        print("\n=== Available Execution IDs ===")
        cursor.execute('''
            SELECT DISTINCT suite_id, test_execution_id, filename
            FROM execution_tracking
            WHERE filename LIKE '%Delivery%' OR filename LIKE '%CNC%'
            ORDER BY suite_id
        ''')

        execution_ids = cursor.fetchall()
        for row in execution_ids:
            print(f"Suite ID: {row['suite_id']}")
            print(f"Test Execution ID: {row['test_execution_id']}")
            print(f"Filename: {row['filename']}")
            print("---")

        # Also show all unique filenames in the database
        print("\n=== All Unique Filenames in Database ===")
        cursor.execute('''
            SELECT DISTINCT filename
            FROM execution_tracking
            ORDER BY filename
        ''')

        all_filenames = cursor.fetchall()
        for row in all_filenames:
            print(f"Filename: {row['filename']}")

        # Also check what the resolve function would return for the execution ID from the report
        print("\n=== Testing Execution ID Resolution ===")
        test_execution_id = "testsuite_execution_20250627_181306"
        print(f"Testing resolution for: {test_execution_id}")

        # Simulate the resolve_execution_id_to_suite_id function
        if 'testsuite_execution_' in test_execution_id:
            timestamp_part = test_execution_id.replace('testsuite_execution_', '')
            cursor.execute('''
                SELECT DISTINCT suite_id, test_execution_id
                FROM execution_tracking
                WHERE test_execution_id LIKE ? OR test_execution_id LIKE ?
                ORDER BY id DESC
                LIMIT 1
            ''', (f'%{timestamp_part}%', f'%{test_execution_id}%'))

            result = cursor.fetchone()
            if result:
                resolved_suite_id = result['suite_id']
                print(f"Resolved to suite_id: {resolved_suite_id}")
            else:
                print(f"No resolution found, would use original: {test_execution_id}")
                resolved_suite_id = test_execution_id
        else:
            resolved_suite_id = test_execution_id
            print(f"No resolution needed, using: {resolved_suite_id}")
        
        if not execution_ids:
            print("No execution data found for Delivery & CNC test case")
            return
        
        # Use the resolved suite ID for testing
        if 'resolved_suite_id' in locals():
            target_suite_id = resolved_suite_id
        else:
            target_suite_id = execution_ids[0]['suite_id']
        target_filename = execution_ids[0]['filename']

        print(f"\n=== Examining data for Suite ID: {target_suite_id}, Filename: {target_filename} ===")

        # Test the get_final_test_case_status logic with the resolved suite ID
        print(f"\n=== Testing get_final_test_case_status function logic ===")

        # Get all actions for this test case using the same query as get_final_test_case_status
        cursor.execute('''
            SELECT action_id, status, retry_count, end_time, action_type, step_idx
            FROM execution_tracking
            WHERE suite_id = ? AND filename = ?
            ORDER BY action_id, retry_count DESC
        ''', (target_suite_id, target_filename))
        
        rows = cursor.fetchall()

        print(f"\nFound {len(rows)} execution entries:")

        # Simulate the exact logic from get_final_test_case_status
        action_statuses = {}
        for row in rows:
            action_id = row['action_id']
            if action_id not in action_statuses:
                # First entry for this action_id (highest retry_count due to ORDER BY)
                action_statuses[action_id] = {
                    'status': row['status'],
                    'retry_count': row['retry_count'],
                    'end_time': row['end_time'],
                    'action_type': row['action_type']
                }

        # Determine overall test case status using the exact logic from get_final_test_case_status
        final_statuses = [action['status'] for action in action_statuses.values()]

        print(f"\nFinal statuses from get_final_test_case_status logic: {final_statuses}")

        if any(status == 'failed' for status in final_statuses):
            overall_status = 'failed'
        elif any(status == 'passed' for status in final_statuses):
            overall_status = 'passed'
        elif any(status == 'running' for status in final_statuses):
            overall_status = 'running'
        else:
            overall_status = 'unknown'

        print(f"Overall status from get_final_test_case_status logic: {overall_status}")

        # Group by action_id to see retry patterns (for detailed analysis)
        action_groups = {}
        for row in rows:
            action_id = row['action_id']
            if action_id not in action_groups:
                action_groups[action_id] = []
            action_groups[action_id].append(dict(row))
        
        print(f"\nGrouped into {len(action_groups)} unique actions:")
        
        failed_actions = 0
        passed_actions = 0
        
        for action_id, entries in action_groups.items():
            # Sort by retry_count descending to get latest status first
            entries.sort(key=lambda x: x['retry_count'], reverse=True)
            latest_entry = entries[0]
            
            print(f"\nAction ID: {action_id}")
            print(f"  Type: {latest_entry['action_type']}")
            print(f"  Step: {latest_entry['step_idx']}")
            print(f"  Latest Status: {latest_entry['status']} (retry {latest_entry['retry_count']})")
            
            if len(entries) > 1:
                print(f"  Retry History:")
                for entry in entries:
                    print(f"    Retry {entry['retry_count']}: {entry['status']}")
            
            # Count final statuses
            if latest_entry['status'] == 'failed':
                failed_actions += 1
            elif latest_entry['status'] == 'passed':
                passed_actions += 1
        
        print(f"\n=== SUMMARY ===")
        print(f"Total Actions: {len(action_groups)}")
        print(f"Failed Actions: {failed_actions}")
        print(f"Passed Actions: {passed_actions}")
        
        # Current logic result
        if failed_actions > 0:
            current_logic_result = "failed"
        elif passed_actions > 0:
            current_logic_result = "passed"
        else:
            current_logic_result = "unknown"
        
        print(f"Current Logic Result: {current_logic_result}")
        
        # Proposed logic result
        if failed_actions == 0 and passed_actions > 0:
            proposed_logic_result = "passed"
        elif failed_actions > 0:
            proposed_logic_result = "failed"
        else:
            proposed_logic_result = "unknown"
        
        print(f"Proposed Logic Result: {proposed_logic_result}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error examining database: {str(e)}")

if __name__ == "__main__":
    examine_test_data()
